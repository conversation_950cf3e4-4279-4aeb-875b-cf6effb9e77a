// 登录按钮点击事件
document.getElementById('loginBtn').addEventListener('click', function() {
    const username = document.getElementById('username').value;
    const employeeId = document.getElementById('employeeId').value;
    const password = document.getElementById('password').value;
    
    if (username && employeeId && password) {
        document.getElementById('loginPage').style.display = 'none';
        document.getElementById('appPage').style.display = 'block';
    } else {
        alert('请输入用户名、工号和密码');
    }
});

// 菜单与页面id的映射
const menuPageMap = {
    '我的首页': 'personal-page',
    '流程任务': 'procedure-assignment',
    '价值工时': 'value-hour',
    '价值工时汇总': 'value-hour-add',
    '产值汇总统计': 'output-add',
    '个人奖金': 'personal-reward',
    '我的价值工时': 'my-value-hours',
    '统计汇总': 'statistical-summary',
    '标准工时配置': 'statitic-hour',
    '工资单位配置': 'wage-units',
    '年度资金分配': 'annual-fund',
    '地区战略分配': 'regional-strategic'
};
let currentSummaryTaskId = null;
let currentProjectId = null;

// ========== 页面切换 ==========
document.addEventListener('DOMContentLoaded', function() {
    // 等待DOM完全加载后再初始化
    setTimeout(() => {
        initPersonalPage();
    }, 100);
});

// 修改页面切换逻辑，确保个人首页正确显示
document.querySelectorAll('.sidebar-menu .menu-item').forEach(item => {
    item.addEventListener('click', function() {
        document.querySelectorAll('.sidebar-menu .menu-item').forEach(i => i.classList.remove('active'));
        this.classList.add('active');
        
        // 隐藏所有页面
        const pages = ['personal-page', 'procedure-assignment', 'value-hour', 'value-hour-add', 'output-add', 'personal-reward', 'my-value-hours', 'statistical-summary', 'statitic-hour', 'wage-units', 'annual-fund', 'regional-strategic'];
        pages.forEach(pageId => {
            const page = document.getElementById(pageId);
            if (page) page.style.display = 'none';
        });
        
        const pageId = this.getAttribute('data-page');
        if (pageId) {
            const page = document.getElementById(pageId);
            if (page) {
                page.style.display = 'block';
                
                // 特别处理个人首页
                if (pageId === 'personal-page') {
                    console.log('显示个人首页，初始化日历');
                    setTimeout(() => {
                        initPersonalPage();
                    }, 50);
                }

                // 特别处理流程任务页面
                if (pageId === 'procedure-assignment') {
                    console.log('显示流程任务页面，初始化表格');
                    setTimeout(() => {
                        renderProcessTaskSummaryTable();
                    }, 50);
                }

                // 特别处理我的价值工时页面
                if (pageId === 'my-value-hours') {
                    console.log('显示我的价值工时页面，初始化表格');
                    setTimeout(() => {
                        renderValueHoursTable();
                    }, 50);
                }
            }
        }
    });
});
// ========== 个人首页日历图 ==========
// 模拟工时数据
const workHourData = {
    '2025-01-01': 0,    // 元旦
    '2025-01-02': 8.5,
    '2025-01-03': 9.2,
    '2025-01-06': 7.8,
    '2025-01-07': 8.0,
    '2025-01-08': 9.5,
    '2025-01-09': 8.3,
    '2025-01-10': 7.5,
    '2025-01-13': 8.8,
    '2025-01-14': 9.0,
    '2025-01-15': 8.2,
    '2025-01-16': 7.9,
    '2025-01-17': 8.6,
    '2025-01-20': 9.3,
    '2025-01-21': 8.1,
    '2025-01-22': 8.7,
    '2025-01-23': 9.1,
    '2025-01-24': 8.4,
    // 2025年7月工时数据
    '2025-07-01': 8.2,
    '2025-07-02': 9.1,
    '2025-07-03': 8.5,
    '2025-07-04': 8.8,
    '2025-07-07': 9.2,
    '2025-07-08': 8.0,
    '2025-07-09': 8.6,
    '2025-07-10': 9.3,
    '2025-07-11': 8.1,
    '2025-07-14': 8.9,
    '2025-07-15': 8.4,
    '2025-07-16': 9.0,
    '2025-07-17': 8.7,
    '2025-07-18': 8.3,
    '2025-07-21': 9.1,
    '2025-07-22': 8.5,
    '2025-07-23': 8.8,
    '2025-07-24': 9.2,
    '2025-07-25': 8.0,
    '2025-07-28': 8.6,
    '2025-07-29': 9.4,
    '2025-07-30': 8.2,
    '2025-07-31': 8.9,
    // 2025年8月工时数据
    '2025-08-01': 8.7,
    '2025-08-04': 9.0,
    '2025-08-05': 8.3,
    '2025-08-06': 8.8,
    '2025-08-07': 9.1,
    '2025-08-08': 8.4,
    '2025-08-11': 8.9,
    '2025-08-12': 8.1,
    '2025-08-13': 9.3,
    '2025-08-14': 8.6,
    '2025-08-15': 8.2,
    '2025-08-18': 9.2,
    '2025-08-19': 8.5,
    '2025-08-20': 8.8,
    '2025-08-21': 9.0,
    '2025-08-22': 8.3,
    '2025-08-25': 8.7,
    '2025-08-26': 9.1,
    '2025-08-27': 8.4,
    '2025-08-28': 8.9,
    '2025-08-29': 8.6
};

// 日历备注数据
const calendarNotes = {};

let currentCalendarDate = new Date();

// 初始化个人首页
function initPersonalPage() {
    console.log('开始初始化个人首页');
    const calendarContainer = document.getElementById('workHourCalendar');
    console.log('日历容器:', calendarContainer);

    if (calendarContainer) {
        renderWorkHourCalendar();
    } else {
        console.error('找不到日历容器 #workHourCalendar');
    }

    // 初始化个人任务表格
    initPersonalTasks();
}

// 渲染工时分布日历
function renderWorkHourCalendar() {
    const calendarContainer = document.getElementById('workHourCalendar');
    if (!calendarContainer) return;
    
    const year = currentCalendarDate.getFullYear();
    const month = currentCalendarDate.getMonth();
    
    calendarContainer.innerHTML = `
        <div class="calendar-header">
            <button class="calendar-nav" onclick="changeCalendarMonth(-1)">‹</button>
            <div class="calendar-title">${year}年${month + 1}月</div>
            <button class="calendar-nav" onclick="changeCalendarMonth(1)">›</button>
        </div>
        <div class="calendar-weekdays">
            <div class="calendar-weekday">日</div>
            <div class="calendar-weekday">一</div>
            <div class="calendar-weekday">二</div>
            <div class="calendar-weekday">三</div>
            <div class="calendar-weekday">四</div>
            <div class="calendar-weekday">五</div>
            <div class="calendar-weekday">六</div>
        </div>
        <div class="calendar-days" id="calendarDays"></div>
        <div class="calendar-legend">
            <div class="legend-item">
                <div class="legend-color legend-none"></div>
                <span>无工时</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-light"></div>
                <span>轻度工作 (≤8h)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-normal"></div>
                <span>正常工作 (8-9h)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color legend-heavy"></div>
                <span>重度工作 (>9h)</span>
            </div>
        </div>
    `;
    
    renderCalendarDays(year, month);
}

// 渲染日历天数
function renderCalendarDays(year, month) {
    const calendarDays = document.getElementById('calendarDays');
    if (!calendarDays) return;
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    
    calendarDays.innerHTML = '';
    
    for (let i = 0; i < 42; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);
        
        const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;
        const workHours = workHourData[dateStr] || 0;
        const isCurrentMonth = currentDate.getMonth() === month;
        const isToday = dateStr === todayStr;
        const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6;
        
        let dayClass = 'calendar-day';
        if (!isCurrentMonth) dayClass += ' other-month';
        if (isToday) dayClass += ' today';
        
        // 根据工时设置样式
        if (workHours === 0 && isCurrentMonth && !isWeekend) {
            dayClass += ' no-work';
        } else if (workHours > 0 && workHours <= 8) {
            dayClass += ' light-work';
        } else if (workHours > 8 && workHours <= 9) {
            dayClass += ' has-work';
        } else if (workHours > 9) {
            dayClass += ' heavy-work';
        }
        
        const dayElement = document.createElement('div');
        dayElement.className = dayClass;

        // 检查是否有备注
        const note = calendarNotes[dateStr];
        const noteDisplay = note ? (note.length > 8 ? note.substring(0, 8) + '...' : note) : '';

        dayElement.innerHTML = `
            <div class="calendar-day-number">${currentDate.getDate()}</div>
            ${workHours > 0 ? `<div class="calendar-day-hours">${workHours}h</div>` : ''}
            ${noteDisplay ? `<div class="calendar-day-note">${noteDisplay}</div>` : ''}
        `;

        // 添加点击事件
        dayElement.addEventListener('click', () => {
            if (isCurrentMonth) {
                showCalendarNoteModal(dateStr, workHours);
            }
        });
        
        calendarDays.appendChild(dayElement);
    }
}

// 切换月份
window.changeCalendarMonth = function(direction) {
    currentCalendarDate.setMonth(currentCalendarDate.getMonth() + direction);
    renderWorkHourCalendar();
};

// 显示日历备注模态框
function showCalendarNoteModal(dateStr, workHours) {
    const date = new Date(dateStr);
    const dateDisplay = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;

    document.getElementById('calendarNoteModal').style.display = 'flex';
    document.getElementById('calendarNoteDate').value = dateDisplay;
    document.getElementById('calendarNoteHours').value = workHours > 0 ? `${workHours}小时` : '无工时记录';
    document.getElementById('calendarNoteContent').value = calendarNotes[dateStr] || '';

    // 保存当前日期用于保存备注
    document.getElementById('calendarNoteModal').setAttribute('data-date', dateStr);
}

// 关闭日历备注模态框
window.closeCalendarNoteModal = function() {
    document.getElementById('calendarNoteModal').style.display = 'none';
};

// 保存日历备注
document.addEventListener('DOMContentLoaded', function() {
    const saveCalendarNoteBtn = document.getElementById('saveCalendarNoteBtn');
    if (saveCalendarNoteBtn) {
        saveCalendarNoteBtn.onclick = function() {
            const modal = document.getElementById('calendarNoteModal');
            const dateStr = modal.getAttribute('data-date');
            const noteContent = document.getElementById('calendarNoteContent').value.trim();

            if (noteContent) {
                calendarNotes[dateStr] = noteContent;
            } else {
                delete calendarNotes[dateStr];
            }

            closeCalendarNoteModal();
            renderWorkHourCalendar(); // 重新渲染日历以显示备注
        };
    }
});

// ========== 个人任务表格 ==========
// 个人任务数据
const personalTasks = {
    todo: [
        { id: 1, name: '项目需求分析', status: '执行中' },
        { id: 2, name: '系统设计评审', status: '提交' }
    ],
    done: [
        { id: 3, name: '代码开发完成', status: '审核' },
        { id: 4, name: '测试用例编写', status: '审批' }
    ],
    initiated: [
        { id: 5, name: '新功能开发申请', status: '审核' },
        { id: 6, name: '技术方案评审', status: '提交' }
    ],
    completed: [
        { id: 7, name: '项目验收完成', status: '审批' },
        { id: 8, name: '文档归档整理', status: '审批' }
    ]
};

let currentPersonalTaskType = '';
let editingPersonalTaskId = null;

// 初始化个人任务表格
function initPersonalTasks() {
    renderPersonalTaskTable('todo');
    renderPersonalTaskTable('done');
    renderPersonalTaskTable('initiated');
    renderPersonalTaskTable('completed');
}

// 渲染个人任务表格
function renderPersonalTaskTable(type) {
    const tableId = type + 'TaskTable';
    const tbody = document.querySelector(`#${tableId} tbody`);
    if (!tbody) return;

    tbody.innerHTML = '';
    personalTasks[type].forEach((task, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${task.name}</td>
            <td>${task.status}</td>
            <td class="action-cell">
                <button class="action-btn edit-btn" onclick="editPersonalTask('${type}', ${task.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="action-btn delete-btn" onclick="deletePersonalTask('${type}', ${task.id})">
                    <i class="fas fa-trash-alt"></i> 删除
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 打开个人任务模态框
window.openPersonalTaskModal = function(type) {
    currentPersonalTaskType = type;
    editingPersonalTaskId = null;

    document.getElementById('personalTaskModal').style.display = 'flex';
    document.getElementById('personalTaskModalTitle').innerText = '新增流程';
    document.getElementById('personalTaskName').value = '';
    document.getElementById('personalTaskStatus').value = '执行中';
};

// 编辑个人任务
window.editPersonalTask = function(type, taskId) {
    const task = personalTasks[type].find(t => t.id === taskId);
    if (!task) return;

    currentPersonalTaskType = type;
    editingPersonalTaskId = taskId;

    document.getElementById('personalTaskModal').style.display = 'flex';
    document.getElementById('personalTaskModalTitle').innerText = '编辑流程';
    document.getElementById('personalTaskName').value = task.name;
    document.getElementById('personalTaskStatus').value = task.status;
};

// 删除个人任务
window.deletePersonalTask = function(type, taskId) {
    if (confirm('确定删除该流程？')) {
        const index = personalTasks[type].findIndex(t => t.id === taskId);
        if (index !== -1) {
            personalTasks[type].splice(index, 1);
            renderPersonalTaskTable(type);
        }
    }
};

// 关闭个人任务模态框
window.closePersonalTaskModal = function() {
    document.getElementById('personalTaskModal').style.display = 'none';
};



// ========== 任务总表 ==========
let summaryTasks = [];
let editingSummaryIdx = null;

// 初始化时从HTML读取静态内容
function initSummaryTasks() {
    summaryTasks = [];
    const trs = document.querySelectorAll('#taskSummaryTable tbody tr');
    trs.forEach(tr => {
        const tds = tr.querySelectorAll('td');
        if (tds.length >= 12) {
            summaryTasks.push({
                taskId: tds[0].textContent,
                feeRate: tds[1].textContent,
                taskAmount: tds[2].textContent,
                plannedHours: tds[3].textContent,
                theoryHours: tds[4].textContent,
                reportedHours: tds[5].textContent,
                confirmedHours: tds[6].textContent,
                trafficFee: tds[7].textContent,
                hotelFee: tds[8].textContent,
                otherFee: tds[9].textContent
            });
        }
    });
}

function openSummaryModal(isEdit, idx) {
    document.getElementById('summaryTaskModal').style.display = 'flex';
    document.getElementById('summaryModalTitle').innerText = isEdit ? '编辑总任务' : '新增总任务';
    if (isEdit && idx !== null) {
        const t = summaryTasks[idx];
        document.getElementById('summaryTaskId').value = t.taskId || '';
        document.getElementById('summaryFeeRate').value = t.feeRate || '';
        document.getElementById('summaryAmount').value = t.taskAmount || '';
        document.getElementById('summaryPlannedHours').value = t.plannedHours || '';
        document.getElementById('summaryTheoryHours').value = t.theoryHours || '';
        document.getElementById('summaryReportedHours').value = t.reportedHours || '';
        document.getElementById('summaryConfirmedHours').value = t.confirmedHours || '';
        document.getElementById('summaryTrafficFee').value = t.trafficFee || '';
        document.getElementById('summaryHotelFee').value = t.hotelFee || '';
        document.getElementById('summaryOtherFee').value = t.otherFee || '';
        editingSummaryIdx = idx;
    } else {
        document.querySelectorAll('#summaryTaskModal input').forEach(i=>i.value='');
        editingSummaryIdx = null;
    }
}
function closeSummaryModal() {
    document.getElementById('summaryTaskModal').style.display = 'none';
}
document.getElementById('addSummaryTaskBtn').onclick = function() {
    openSummaryModal(false, null);
};
document.getElementById('saveSummaryTaskBtn').onclick = function() {
    const t = {
        taskId: document.getElementById('summaryTaskId').value,
        feeRate: document.getElementById('summaryFeeRate').value,
        taskAmount: document.getElementById('summaryAmount').value,
        plannedHours: document.getElementById('summaryPlannedHours').value,
        theoryHours: document.getElementById('summaryTheoryHours').value,
        reportedHours: document.getElementById('summaryReportedHours').value,
        confirmedHours: document.getElementById('summaryConfirmedHours').value,
        trafficFee: document.getElementById('summaryTrafficFee').value,
        hotelFee: document.getElementById('summaryHotelFee').value,
        otherFee: document.getElementById('summaryOtherFee').value
    };
    if (editingSummaryIdx !== null) {
        summaryTasks[editingSummaryIdx] = t;
    } else {
        summaryTasks.push(t);
    }
    closeSummaryModal();
    renderSummaryTable();
};
window.editSummaryTask = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    openSummaryModal(true, idx);
};
window.deleteSummaryTask = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    if (confirm('确定删除该总任务？')) {
        summaryTasks.splice(idx, 1);
        renderSummaryTable();
    }
};
function renderSummaryTable() {
    const tbody = document.querySelector('#taskSummaryTable tbody');
    tbody.innerHTML = '';
    summaryTasks.forEach((t, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${t.taskId}</td>
            <td>${t.feeRate}</td>
            <td>${t.taskAmount}</td>
            <td>${t.plannedHours}</td>
            <td>${t.theoryHours}</td>
            <td>${t.reportedHours}</td>
            <td>${t.confirmedHours}</td>
            <td>${t.trafficFee}</td>
            <td>${t.hotelFee}</td>
            <td>${t.otherFee}</td>
            <td>
                <button class="action-btn edit-btn" onclick="editSummaryTask(this)"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteSummaryTask(this)"><i class="fas fa-trash-alt"> 删除</i></button>
            </td>
            <td>
                <button class="btn btn-outline" onclick="viewProjectTasks()">查看</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// ========== 项目任务表 ==========
let projectRows = [];
let editingProjectIdx = null;
function initProjectRows() {
    projectRows = [];
    const trs = document.querySelectorAll('#projectTable tbody tr');
    trs.forEach(tr => {
        const tds = tr.querySelectorAll('td');
        if (tds.length >= 12) {
            projectRows.push({
                contractId: tds[0].textContent,
                orderId: tds[1].textContent,
                orderType: tds[2].textContent,
                orderAmount: tds[3].textContent,
                allocatedAmount: tds[4].textContent,
                department: tds[5].textContent,
                group: tds[6].textContent,
                standardHours: tds[7].textContent,
                efficiency: tds[8].textContent,
                orderStatus: tds[9].textContent,
                createTime: tds[10].textContent
            });
        }
    });
}
function openProjectModal(isEdit, idx) {
    document.getElementById('projectModal').style.display = 'flex';
    document.getElementById('projectModalTitle').innerText = isEdit ? '编辑项目' : '新增项目';
    if (isEdit && idx !== null) {
        const p = projectRows[idx];
        document.getElementById('projectContractId').value = p.contractId || '';
        document.getElementById('projectOrderId').value = p.orderId || '';
        document.getElementById('projectOrderType').value = p.orderType || '';
        document.getElementById('projectOrderAmount').value = p.orderAmount || '';
        document.getElementById('projectAllocatedAmount').value = p.allocatedAmount || '';
        document.getElementById('projectDepartment').value = p.department || '';
        document.getElementById('projectGroup').value = p.group || '';
        document.getElementById('projectStandardHours').value = p.standardHours || '';
        document.getElementById('projectEfficiency').value = p.efficiency || '';
        document.getElementById('projectOrderStatus').value = p.orderStatus || '';
        document.getElementById('projectCreateTime').value = p.createTime || '';
        editingProjectIdx = idx;
    } else {
        document.querySelectorAll('#projectModal input').forEach(i=>i.value='');
        editingProjectIdx = null;
    }
}
function closeProjectModal() {
    document.getElementById('projectModal').style.display = 'none';
}
document.getElementById('addProjectBtn').onclick = function() {
    openProjectModal(false, null);
};
document.getElementById('saveProjectBtn').onclick = function() {
    const p = {
        contractId: document.getElementById('projectContractId').value,
        orderId: document.getElementById('projectOrderId').value,
        orderType: document.getElementById('projectOrderType').value,
        orderAmount: document.getElementById('projectOrderAmount').value,
        allocatedAmount: document.getElementById('projectAllocatedAmount').value,
        department: document.getElementById('projectDepartment').value,
        group: document.getElementById('projectGroup').value,
        standardHours: document.getElementById('projectStandardHours').value,
        efficiency: document.getElementById('projectEfficiency').value,
        orderStatus: document.getElementById('projectOrderStatus').value,
        createTime: document.getElementById('projectCreateTime').value
    };
    if (editingProjectIdx !== null) {
        projectRows[editingProjectIdx] = p;
    } else {
        projectRows.push(p);
    }
    closeProjectModal();
    renderProjectTable();
};

window.editProjectRow = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    openProjectModal(true, idx);
};
window.deleteProjectRow = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    if (confirm('确定删除该项目？')) {
        projectRows.splice(idx, 1);
        renderProjectTable();
    }
};
function renderProjectTable() {
    const tbody = document.querySelector('#projectTable tbody');
    tbody.innerHTML = '';
    projectRows.forEach((p, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${p.contractId}</td>
            <td>${p.orderId}</td>
            <td>${p.orderType}</td>
            <td>${p.orderAmount}</td>
            <td>${p.allocatedAmount}</td>
            <td>${p.department}</td>
            <td>${p.group}</td>
            <td>${p.standardHours}</td>
            <td>${p.efficiency}</td>
            <td>${p.orderStatus}</td>
            <td>${p.createTime}</td>
            <td>
                <button class="action-btn edit-btn" onclick="editProjectRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteProjectRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
            </td>
            <td>
                <button class="btn btn-outline" onclick="showProjectTasks('${p.contractId}')">查看</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// ========== 任务信息表 ==========
//设置奖惩系数相关范围(任务信息)

let rewardBaseValueStat = 400;
let rewardMinLimitStat = 200;
let rewardMinFactorStat = 0;
let rewardMaxLimitStat = 900;
let rewardMaxFactorStat = 1.5;
let rewardRangesStat = [
    { min: 200, max: 300, factor: 0.6 },
    { min: 300, max: 400, factor: 0.8 },
    { min: 400, max: 500, factor: 1 },
    { min: 500, max: 600, factor: 1.1 },
    { min: 600, max: 700, factor: 1.2 },
    { min: 700, max: 800, factor: 1.3 },
    { min: 800, max: 900, factor: 1.4 }
];
let allTasks = [
    {
        projectId: 'CD2019051335SX',
        seq:1,
        taskId: '303262',
        feeRate: '506',
        empId: 'HMC0708',
        empName: '直接用',
        department: '西部事业部',
        group: '成都区域',
        taskAmount: 3978,
        plannedHours: 8,
        additionalHours: 7.82,
        extraHours: 10.2,
        valueHour: 7.85,
        confirmedHours: 8,
        valueHour: 7.85, // 计算得到的值
        cost: 100,
        hourUtilization: 0.98,
    },
    {
        projectId: 'CD2019052140XX',
        seq:1,
        taskId: '303262',
        feeRate: '506',
        empId: 'HMC0708',
        empName: '直接用',
        department: '西部事业部',
        group: '成都区域',
        taskAmount: 3978,
        plannedHours: 8,
        additionalHours: 7.82,
        extraHours: 10.2,
        valueHour: 7.85,
        confirmedHours: 8,
        valueHour: 7.85, // 计算得到的值
        cost: 100,
        hourUtilization: 0.98,
    },
    // 一级流程任务数据
    {
        projectId: '1',
        seq: 1,
        taskId: '客户准护',
        feeRate: '100',
        empId: 'EMP001',
        empName: '王勇',
        department: '分中心营销部',
        group: '营销组',
        taskAmount: 1000,
        plannedHours: 10,
        additionalHours: 10,
        extraHours: 10,
        valueHour: 10,
        confirmedHours: 10,
        cost: 50,
        hourUtilization: 1.0,
    },
    {
        projectId: '2',
        seq: 1,
        taskId: '任务安排+流程跟踪',
        feeRate: '120',
        empId: 'EMP002',
        empName: '魏新娟',
        department: '分中心营销部',
        group: '营销组',
        taskAmount: 1200,
        plannedHours: 12,
        additionalHours: 7.2,
        extraHours: 12,
        valueHour: 10,
        confirmedHours: 12,
        cost: 60,
        hourUtilization: 0.6,
    },
    {
        projectId: '3',
        seq: 1,
        taskId: '工厂检查',
        feeRate: '150',
        empId: 'EMP003',
        empName: '于兰',
        department: '分中心检查部',
        group: '检查组',
        taskAmount: 5000,
        plannedHours: 50,
        additionalHours: 20,
        extraHours: 50,
        valueHour: 33.33,
        confirmedHours: 50,
        cost: 200,
        hourUtilization: 0.4,
    },
    {
        projectId: '4',
        seq: 1,
        taskId: '工厂检查报告审核与认证',
        feeRate: '130',
        empId: 'EMP002',
        empName: '魏新娟',
        department: '分中心营销部',
        group: '营销组',
        taskAmount: 1500,
        plannedHours: 15,
        additionalHours: 5.25,
        extraHours: 15,
        valueHour: 11.54,
        confirmedHours: 15,
        cost: 75,
        hourUtilization: 0.35,
    },
    {
        projectId: '5',
        seq: 1,
        taskId: '证书处理',
        feeRate: '80',
        empId: 'EMP002',
        empName: '魏新娟',
        department: '分中心营销部',
        group: '营销组',
        taskAmount: 500,
        plannedHours: 5,
        additionalHours: 1.5,
        extraHours: 5,
        valueHour: 6.25,
        confirmedHours: 5,
        cost: 25,
    },
    {
        projectId: 'CD2019052140XX',
        seq:1,
        taskId: '303262',
        feeRate: '506',
        empId: 'HMC0708',
        empName: '直接用',
        department: '西部事业部',
        group: '成都区域',
        taskAmount: 3978,
        plannedHours: 8,
        additionalHours: 7.82,
        extraHours: 10.2,
        valueHour: 7.85,
        confirmedHours: 8,
        valueHour: 7.85, // 计算得到的值
        cost: 100,
        hourUtilization: 0.98,
    }
];


function renderTaskTable(projectId) {
    const tbody = document.querySelector('#taskTable tbody');
    tbody.innerHTML = '';
    // 只渲染当前项目的任务
    allTasks
        .filter(task => task.projectId === projectId)
        .forEach((task, idx) => {
            let feeRate = parseFloat(task.feeRate) || 0;
            let taskAmount = parseFloat(task.taskAmount) || 0;
            let valueHour = (feeRate > 0) ? (taskAmount / feeRate) : 0; // 不要toFixed
            let cost = parseFloat(task.cost) || 0;
            let denominator = (valueHour * feeRate) + cost;
            let reCoverRate = denominator > 0 ? (taskAmount / denominator).toFixed(2) : '';
            let extraHours = parseFloat(task.extraHours) || 0;
            let hourUtilization = (extraHours > 0) ? (valueHour / extraHours).toFixed(2) : '';
            const tr = document.createElement('tr');
            tr.setAttribute('data-project-id', task.projectId);
            tr.innerHTML = `
                <td>${idx + 1}</td>
                <td>${task.taskId}</td>
                <td>${task.feeRate}</td>
                <td>${task.empId}</td>
                <td>${task.empName}</td>
                <td>${task.department}</td>
                <td>${task.group}</td>
                <td>${task.taskAmount}</td>
                <td>${task.plannedHours}</td>
                <td>${task.additionalHours}</td>
                <td>${task.extraHours}</td>
                <td>${valueHour ? valueHour.toFixed(2) : ''}</td>
                <td>${task.confirmedHours}</td>
                <td>${cost}</td>
                <td>${reCoverRate}</td>
                <td>${hourUtilization}</td>
                <td class="action-cell">
                    <button class="action-btn edit-btn" onclick="editTask(this)">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="action-btn delete-btn" onclick="deleteTask(this)">
                        <i class="fas fa-trash-alt"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(tr);
        });
}
//计算奖惩系数
// function getRewardFactor(valueHour) {
//     if (valueHour < rewardMinLimit) return rewardMinFactor;
//     if (valueHour >= rewardMaxLimit) return rewardMaxFactor;
//     for (let r of rewardRanges) {
//         if (valueHour >= r.min && valueHour < r.max) return r.factor;
//     }
//     return '';
// }
//自动计算函数
function autoCalc() {
    const amount = parseFloat(document.getElementById('taskAmount').value) || 0;
    const feeRate = parseFloat(document.getElementById('feeRate').value) || 0;
    const cost = parseFloat(document.getElementById('cost').value) || 0;
    const valueHour = (feeRate > 0) ? (amount / feeRate) : 0;
    document.getElementById('valueHourDisplay').value = valueHour ? valueHour.toFixed(2) : '';
    // recoverRate = 任务金额 / [(价值工时 × 费率) + 消耗费用]
    const denominator = (valueHour * feeRate) + cost;
    const recoverRate = denominator > 0 ? (amount / denominator).toFixed(2) : '';
    const hourUtilization = (document.getElementById('extraHours').value > 0) ? (valueHour / document.getElementById('extraHours').value).toFixed(2) : '';
    if(document.getElementById('recoverRate')) {
        document.getElementById('recoverRate').value = recoverRate;
    }
    if(document.getElementById('hourUtilization')) {
        document.getElementById('hourUtilization').value = hourUtilization;
    }
    // 自动计算奖惩系数
    // if(document.getElementById('rewardFactor')) {
    //     document.getElementById('rewardFactor').value = getRewardFactor(valueHour);
    // }
}
document.getElementById('taskAmount').addEventListener('input', autoCalc);
document.getElementById('feeRate').addEventListener('input', autoCalc);
document.getElementById('cost').addEventListener('input', autoCalc);
document.getElementById('extraHours').addEventListener('input', autoCalc);

// 编辑和删除任务行
document.getElementById('addTaskBtn').onclick = function() {
    document.getElementById('taskModal').style.display = 'flex';
    document.getElementById('modalTitle').innerText = '新增任务';
    document.querySelectorAll('#taskModal input, #taskModal select').forEach(i => i.value = '');
    document.getElementById('saveTaskBtn').onclick = function() {
        // 获取当前项目ID
        let projectId = '';
        const projectIdSpan = document.getElementById('currentProjectId');
        if (projectIdSpan) {
            projectId = projectIdSpan.textContent.replace('（项目编号：', '').replace('）', '');
        }
        // 获取表单数据
        const task = {
            projectId: projectId,
            taskId: document.getElementById('taskId').value,
            feeRate: document.getElementById('feeRate').value,
            empId: document.getElementById('empId').value,
            empName: document.getElementById('empName').value,
            department: document.getElementById('department').value,
            group: document.getElementById('group').value,
            taskAmount: document.getElementById('taskAmount').value,
            plannedHours: document.getElementById('plannedHours').value,
            additionalHours: document.getElementById('additionalHours').value,
            valueHour: document.getElementById('valueHourDisplay').value, // 自动算
            extraHours: document.getElementById('extraHours').value,
            confirmedHours: document.getElementById('confirmedHours').value,
            cost: document.getElementById('cost').value, // 手输
            recoverRate: document.getElementById('recoverRate').value,
        };
        allTasks.push(task);
        closeModal();
        renderTaskTable(projectId);
    };
};
//基准系数模块渲染
function openRewardConfigModal(source) {
    document.getElementById('rewardConfigModal').style.display = 'flex';
    document.getElementById('rewardBaseValue').value = rewardBaseValueStat;
    document.getElementById('rewardMinLimit').value = rewardMinLimitStat;
    document.getElementById('rewardMinFactor').value = rewardMinFactorStat;
    document.getElementById('rewardMaxLimit').value = rewardMaxLimitStat;
    document.getElementById('rewardMaxFactor').value = rewardMaxFactorStat;
    renderRewardRangesStat();

    
}
function closeRewardConfigModal() {
    document.getElementById('rewardConfigModal').style.display = 'none';
}
function renderRewardRanges() {
    const container = document.getElementById('rewardRanges');
    container.innerHTML = '';
    rewardRanges.forEach((r, idx) => {
        container.innerHTML += `
        <div class="form-row" style="display:flex;gap:4px;align-items:center;">
            <input type="number" class="form-control" style="width:80px" value="${r.min}" onchange="updateRewardRange(${idx},'min',this.value)">
            ~
            <input type="number" class="form-control" style="width:80px" value="${r.max}" onchange="updateRewardRange(${idx},'max',this.value)">
            =
            <input type="number" class="form-control" style="width:80px" value="${r.factor}" step="0.01" onchange="updateRewardRange(${idx},'factor',this.value)">
            <button class="btn btn-outline btn-mini" onclick="removeRewardRange(${idx})">删除</button>
        </div>
        `;
    });
}

function renderRewardRangesStat() {
    const container = document.getElementById('rewardRanges');
    container.innerHTML = '';
    rewardRangesStat.forEach((r, idx) => {
        container.innerHTML += `
        <div class="form-row" style="display:flex;gap:4px;align-items:center;">
            <input type="number" class="form-control" style="width:80px" value="${r.min}" onchange="updateRewardRange(${idx},'min',this.value)">
            ~
            <input type="number" class="form-control" style="width:80px" value="${r.max}" onchange="updateRewardRange(${idx},'max',this.value)">
            =
            <input type="number" class="form-control" style="width:80px" value="${r.factor}" step="0.01" onchange="updateRewardRange(${idx},'factor',this.value)">
            <button class="btn btn-outline btn-mini" onclick="removeRewardRange(${idx})">删除</button>
        </div>
        `;
    });
}
function updateRewardRange(idx, key, value) {
    rewardRanges[idx][key] = parseFloat(value);
}
function addRewardRange() {
    rewardRanges.push({ min: 0, max: 0, factor: 1 });
    renderRewardRanges();
}
function removeRewardRange(idx) {
    rewardRanges.splice(idx, 1);
    renderRewardRanges();
}
function saveRewardConfig() {
    rewardBaseValue = parseFloat(document.getElementById('rewardBaseValue').value) || 400;
    rewardMinLimit = parseFloat(document.getElementById('rewardMinLimit').value) || 200;
    rewardMinFactor = parseFloat(document.getElementById('rewardMinFactor').value) || 0;
    rewardMaxLimit = parseFloat(document.getElementById('rewardMaxLimit').value) || 900;
    rewardMaxFactor = parseFloat(document.getElementById('rewardMaxFactor').value) || 1.5;
    closeRewardConfigModal();
    if (currentProjectId) {
        renderTaskTable(currentProjectId);
    }
}
// ========== 工时汇总表 ==========
// 工时汇总数据
let workHourRows = [
    {
        empId: 'HMC0708',
        empName: '张三丰',
        department: '西部事业部',
        group: '成都区域',
        month: '2023-10',
        standard: 160,
        actual: 170,
        overtime: 10,
        leave: 5,
        value: 160,
        utilization: '95%'
    },
    {
        empId: 'HMC0709',
        empName: '李四年',
        department: '西部事业部',
        group: '成都区域',
        month: '2023-10',
        standard: 160,
        actual: 150,
        overtime: 5,
        leave: 2,
        value: 120,
        utilization: '90%'
    }
];
let editingWorkHourIdx = null;

// 渲染工时汇总表
function renderWorkHourTable() {
    const tbody = document.querySelector('#workHourSummaryTable tbody');
    tbody.innerHTML = '';
    workHourRows.forEach((row, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${idx + 1}</td>
            <td>${row.empId}</td>
            <td>${row.empName}</td>
            <td>${row.department}</td>
            <td>${row.group}</td>
            <td>${row.month}</td>
            <td>${row.standard}</td>
            <td>${row.actual}</td>
            <td>${row.overtime}</td>
            <td>${row.leave}</td>
            <td>${row.value}</td>
            <td>${row.utilization}</td>
            <td>
                <button class="action-btn edit-btn" onclick="editWorkHourRow(${idx})"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteWorkHourRow(${idx})"><i class="fas fa-trash-alt"></i> 删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
    renderWorkHourChart();
}
let workHourBarChart = null;

function renderWorkHourChart() {
    const ctx = document.getElementById('workHourBarChart').getContext('2d');
    // 获取员工姓名、价值工时、工时利用率
    const labels = workHourRows.map(row => row.empName || row.empId);
    const valueData = workHourRows.map(row => Number(row.value) || 0);
    const actualData = workHourRows.map(row => Number(row.actual) || 0); // 实际工时
    // 如果已存在图表，先销毁
    if (workHourBarChart) {
        workHourBarChart.destroy();
    }

    workHourBarChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '价值工时',
                    data: valueData,
                    backgroundColor: 'rgba(37,99,235,0.7)'
                },
                {
                    label: '实际工时',
                    data: actualData,
                    backgroundColor: 'rgba(245,158,11,0.7)'
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { 
                    position: 'top' ,
                    labels: { 
                        font: { 
                            size: 16 
                        } 
                    },
                },

                title: { 
                    display: true, 
                    text: '员工价值工时与工时利用率' ,
                    font: { 
                        size: 20 
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        font: {
                            size: 14 // X轴字体大小
                        }
                    }
                },
                y: { 
                    beginAtZero: true,
                    ticks: {
                        font: {
                            size: 14 // Y轴字体大小
                        }
                    }
                 }
            }
        }
    });
}

function updateWorkHourUtilization() {
    const value = parseFloat(document.getElementById('whValue').value) || 0;
    const actual = parseFloat(document.getElementById('whActual').value) || 0;
    let utilization = '';
    if (actual > 0) {
        utilization = ((value / actual) * 100).toFixed(2) + '%';
    }
    document.getElementById('whUtilization').value = utilization;
}

// 监听输入变化
document.getElementById('whValue').addEventListener('input', updateWorkHourUtilization);
document.getElementById('whActual').addEventListener('input', updateWorkHourUtilization);
// 打开模态框
function openWorkHourModal(isEdit, idx) {
    document.getElementById('workHourModal').style.display = 'flex';
    document.getElementById('workHourModalTitle').innerText = isEdit ? '编辑工时' : '新增工时';
    if (isEdit && idx !== null) {
        const row = workHourRows[idx];
        document.getElementById('whEmpId').value = row.empId || '';
        document.getElementById('whEmpName').value = row.empName || '';
        document.getElementById('whDepartment').value = row.department || '';
        document.getElementById('whGroup').value = row.group || '';
        document.getElementById('whMonth').value = row.month || '';
        document.getElementById('whStandard').value = row.standard || '';
        document.getElementById('whActual').value = row.actual || '';
        document.getElementById('whOvertime').value = row.overtime || '';
        document.getElementById('whLeave').value = row.leave || '';
        document.getElementById('whValue').value = row.value || '';
        document.getElementById('whUtilization').value = row.utilization || '';
        editingWorkHourIdx = idx;
    } else {
        document.querySelectorAll('#workHourModal input').forEach(i=>i.value='');
        editingWorkHourIdx = null;
    }
    updateWorkHourUtilization();
}
function closeWorkHourModal() {
    document.getElementById('workHourModal').style.display = 'none';
}

// 新增按钮
document.getElementById('addWorkHourBtn').onclick = function() {
    openWorkHourModal(false, null);
};

// 保存按钮
document.getElementById('saveWorkHourBtn').onclick = function() {
    const row = {
        empId: document.getElementById('whEmpId').value,
        empName: document.getElementById('whEmpName').value,
        department: document.getElementById('whDepartment').value,
        group: document.getElementById('whGroup').value,
        month: document.getElementById('whMonth').value,
        standard: document.getElementById('whStandard').value,
        actual: document.getElementById('whActual').value,
        overtime: document.getElementById('whOvertime').value,
        leave: document.getElementById('whLeave').value,
        value: document.getElementById('whValue').value,
        utilization: document.getElementById('whUtilization').value
    };
    if (editingWorkHourIdx !== null) {
        workHourRows[editingWorkHourIdx] = row;
    } else {
        workHourRows.push(row);
    }
    closeWorkHourModal();
    renderWorkHourTable();
};

// 编辑
window.editWorkHourRow = function(idx) {
    openWorkHourModal(true, idx);
};

// 删除
window.deleteWorkHourRow = function(idx) {
    if (confirm('确定删除该工时记录？')) {
        workHourRows.splice(idx, 1);
        renderWorkHourTable();
    }
};

//奖金统计汇总页面
// 统计汇总奖金表格数据 - 按时间类型分组
const statSummaryBonusData = {
    monthly: [
        {
            empId: 'HMC0708',
            empName: '张三丰',
            department: '西部事业部',
            group: '成都区域',
            period: '2023-10',
            baseSalary: 8000,
            valueHour: 800,
            rewardFactor: 1.2,
            baseBonus: 2000,
            performanceBonus: 1500,
            total: 11500
        },
        {
            empId: 'HMC0709',
            empName: '李四年',
            department: '西部事业部',
            group: '重庆区域',
            period: '2023-10',
            baseSalary: 9000,
            valueHour: 600,
            rewardFactor: 1.1,
            baseBonus: 1800,
            performanceBonus: 1200,
            total: 12000
        },
        {
            empId: 'HMC0710',
            empName: '王五',
            department: '华南事业群',
            group: '广州区域',
            period: '2023-10',
            baseSalary: 8500,
            valueHour: 750,
            rewardFactor: 1.15,
            baseBonus: 1900,
            performanceBonus: 1300,
            total: 11800
        },
        {
            empId: 'HMC0711',
            empName: '赵六',
            department: '华东事业群',
            group: '上海区域',
            period: '2023-10',
            baseSalary: 9500,
            valueHour: 850,
            rewardFactor: 1.25,
            baseBonus: 2100,
            performanceBonus: 1600,
            total: 13200
        }
    ],
    quarterly: [
        {
            empId: 'HMC0708',
            empName: '张三丰',
            department: '西部事业部',
            group: '成都区域',
            period: '2023-Q4',
            baseSalary: 24000,
            valueHour: 2400,
            rewardFactor: 1.2,
            baseBonus: 6000,
            performanceBonus: 4500,
            total: 34500
        },
        {
            empId: 'HMC0709',
            empName: '李四年',
            department: '西部事业部',
            group: '重庆区域',
            period: '2023-Q4',
            baseSalary: 27000,
            valueHour: 1800,
            rewardFactor: 1.1,
            baseBonus: 5400,
            performanceBonus: 3600,
            total: 36000
        }
    ],
    yearly: [
        {
            empId: 'HMC0708',
            empName: '张三丰',
            department: '西部事业部',
            group: '成都区域',
            period: '2023',
            baseSalary: 96000,
            valueHour: 9600,
            rewardFactor: 1.2,
            baseBonus: 24000,
            performanceBonus: 18000,
            total: 138000
        }
    ]
};

let currentStatTimeType = 'monthly';
let statSummaryBonusRows = statSummaryBonusData.monthly;
let filteredStatRows = [...statSummaryBonusRows];

// 当前筛选条件
let currentStatFilters = {
    department: '',
    group: ''
};

// 搜索筛选功能
function searchStatData() {
    const departmentFilter = document.getElementById('statDepartmentFilter').value;
    const groupFilter = document.getElementById('statGroupFilter').value;
    
    // 保存当前筛选条件
    currentStatFilters.department = departmentFilter;
    currentStatFilters.group = groupFilter;
    
    // 应用筛选到当前时间类型的数据
    applyStatFilters();
}

// 应用筛选条件到当前数据
function applyStatFilters() {
    filteredStatRows = statSummaryBonusRows.filter(row => {
        const departmentMatch = !currentStatFilters.department || row.department === currentStatFilters.department;
        const groupMatch = !currentStatFilters.group || row.group === currentStatFilters.group;
        return departmentMatch && groupMatch;
    });
    
    renderStatSummaryBonusTable();
}

// 切换统计汇总时间类型
function switchStatTimeType(timeType) {
    currentStatTimeType = timeType;
    statSummaryBonusRows = statSummaryBonusData[timeType];
    
    // 重新应用当前的筛选条件
    applyStatFilters();
    
    // 更新表头
    const periodHeader = document.getElementById('statPeriodHeader');
    switch(timeType) {
        case 'monthly':
            periodHeader.textContent = '月份';
            break;
        case 'quarterly':
            periodHeader.textContent = '季度';
            break;
        case 'yearly':
            periodHeader.textContent = '年度';
            break;
    }
}

// 初始化统计汇总页面时重置筛选条件
function initStatSummary() {
    // 重置筛选条件
    currentStatFilters.department = '';
    currentStatFilters.group = '';
    
    // 重置筛选框的值
    const departmentFilter = document.getElementById('statDepartmentFilter');
    const groupFilter = document.getElementById('statGroupFilter');
    if (departmentFilter) departmentFilter.value = '';
    if (groupFilter) groupFilter.value = '';
    
    // 重置为月度数据
    currentStatTimeType = 'monthly';
    statSummaryBonusRows = statSummaryBonusData.monthly;
    
    // 应用筛选（此时为空筛选，显示所有数据）
    applyStatFilters();
    
    // 重置时间选择器按钮状态
    const statTimeSelector = document.getElementById('statTimeSelector');
    if (statTimeSelector) {
        const buttons = statTimeSelector.querySelectorAll('.time-btn');
        buttons.forEach(button => {
            button.classList.remove('active');
            if (button.getAttribute('data-time-type') === 'monthly') {
                button.classList.add('active');
            }
        });
    }
}

// 统计汇总时间选择器事件监听
document.addEventListener('DOMContentLoaded', function() {
    const statTimeSelector = document.getElementById('statTimeSelector');
    
    if (statTimeSelector) {
        statTimeSelector.addEventListener('click', function(event) {
            const target = event.target;
            
            if (target.classList.contains('time-btn')) {
                // 移除所有按钮的 active 类
                const buttons = statTimeSelector.querySelectorAll('.time-btn');
                buttons.forEach(button => {
                    button.classList.remove('active');
                });
                
                // 为当前点击的按钮添加 active 类
                target.classList.add('active');
                
                // 获取选中的时间类型并切换数据
                const selectedTimeType = target.getAttribute('data-time-type');
                switchStatTimeType(selectedTimeType);
            }
        });
    }
    
    // 添加统计汇总搜索按钮事件监听
    const searchStatBtn = document.getElementById('searchStatBtn');
    if (searchStatBtn) {
        searchStatBtn.addEventListener('click', searchStatData);
    }
});

// 渲染统计汇总奖金表格
function renderStatSummaryBonusTable() {
    const tbody = document.querySelector('#statSummaryBonusTable tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    filteredStatRows.forEach((row, idx) => {
        const performanceBonus = (Number(row.baseBonus) * Number(row.rewardFactor)).toFixed(2);
        const total = (Number(row.baseSalary) + Number(performanceBonus)).toFixed(2);
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.empId}</td>
            <td>${row.empName}</td>
            <td>${row.department}</td>
            <td>${row.group}</td>
            <td>${row.period}</td>
            <td>${row.baseSalary}</td>
            <td>${row.valueHour}</td>
            <td>${row.rewardFactor}</td>
            <td>${row.baseBonus}</td>
            <td>${performanceBonus}</td>
            <td>${total}</td>
            <td>
                <button class="action-btn edit-btn" onclick="editStatBonusRow(${idx})"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteStatBonusRow(${idx})"><i class="fas fa-trash-alt"></i> 删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
    renderStatBonusStackedBarChart();
}
function getStatRewardFactor(valueHour) {
    valueHour = parseFloat(valueHour) || 0;
    if (valueHour < rewardMinLimitStat) return rewardMinFactorStat;
    if (valueHour >= rewardMaxLimitStat) return rewardMaxFactorStat;
    for (let r of rewardRangesStat) {
        if (valueHour >= r.min && valueHour < r.max) return r.factor;
    }
    return '';
}
let editingStatBonusIdx = null;

// 打开模态框
function openStatBonusModal(isEdit, idx) {
    // 更新模态框中的标签
    const periodLabel = document.querySelector('label[for="statMonth"], #statPeriodLabel');
    if (periodLabel) {
        switch(currentStatTimeType) {
            case 'monthly':
                periodLabel.textContent = '月份';
                break;
            case 'quarterly':
                periodLabel.textContent = '季度';
                break;
            case 'yearly':
                periodLabel.textContent = '年度';
                break;
        }
    }
    
    document.getElementById('statBonusModal').style.display = 'flex';
    document.getElementById('statBonusModalTitle').innerText = isEdit ? '编辑奖金明细' : '新增奖金明细';
    if (isEdit && idx !== null) {
        const row = statSummaryBonusRows[idx];
        document.getElementById('statEmpId').value = row.empId || '';
        document.getElementById('statEmpName').value = row.empName || '';
        document.getElementById('statDepartment').value = row.department || '';
        document.getElementById('statGroup').value = row.group || '';
        document.getElementById('statMonth').value = row.period || '';
        document.getElementById('statBaseSalary').value = row.baseSalary || '';
        document.getElementById('statValueHour').value = row.valueHour || '';
        document.getElementById('statRewardFactor').value = row.rewardFactor || '';
        document.getElementById('statBaseBonus').value = row.baseBonus || '';
        updateStatBonusCalc();
        editingStatBonusIdx = idx;
    } else {
        document.querySelectorAll('#statBonusModal input').forEach(i=>i.value='');
        editingStatBonusIdx = null;
    }
}
function closeStatBonusModal() {
    document.getElementById('statBonusModal').style.display = 'none';
}

// 自动计算绩效奖金和总计
function updateStatBonusCalc() {
    const valueHour = parseFloat(document.getElementById('statValueHour').value) || 0;
    const rewardFactor = getStatRewardFactor(valueHour);
    document.getElementById('statRewardFactor').value = rewardFactor;
    const baseBonus = parseFloat(document.getElementById('statBaseBonus').value) || 0;
    const baseSalary = parseFloat(document.getElementById('statBaseSalary').value) || 0;
    const performanceBonus = (baseBonus * rewardFactor).toFixed(2);
    const total = (baseSalary + Number(performanceBonus)).toFixed(2);
    document.getElementById('statPerformanceBonus').value = performanceBonus;
    document.getElementById('statTotal').value = total;
}

// 监听输入变化
['statBaseBonus','statRewardFactor','statBaseSalary'].forEach(id=>{
    document.getElementById(id).addEventListener('input', updateStatBonusCalc);
});

// 保存按钮
document.getElementById('saveStatBonusBtn').onclick = function() {
    const row = {
        empId: document.getElementById('statEmpId').value,
        empName: document.getElementById('statEmpName').value,
        department: document.getElementById('statDepartment').value,
        group: document.getElementById('statGroup').value,
        period: document.getElementById('statMonth').value,
        baseSalary: document.getElementById('statBaseSalary').value,
        valueHour: document.getElementById('statValueHour').value,
        rewardFactor: document.getElementById('statRewardFactor').value,
        baseBonus: document.getElementById('statBaseBonus').value
    };
    
    if (editingStatBonusIdx !== null) {
        statSummaryBonusRows[editingStatBonusIdx] = row;
        statSummaryBonusData[currentStatTimeType][editingStatBonusIdx] = row;
    } else {
        statSummaryBonusRows.push(row);
        statSummaryBonusData[currentStatTimeType].push(row);
    }
    closeStatBonusModal();
    renderStatSummaryBonusTable();
};

// 编辑
window.editStatBonusRow = function(idx) {
    // 找到在原始数据中的索引
    const originalIdx = statSummaryBonusRows.findIndex(row => row === filteredStatRows[idx]);
    openStatBonusModal(true, originalIdx);
};

// 删除
window.deleteStatBonusRow = function(idx) {
    if (confirm('确定删除该奖金记录？')) {
        // 找到在原始数据中的索引
        const originalIdx = statSummaryBonusRows.findIndex(row => row === filteredStatRows[idx]);
        if (originalIdx !== -1) {
            statSummaryBonusRows.splice(originalIdx, 1);
            statSummaryBonusData[currentStatTimeType].splice(originalIdx, 1);
            searchStatData(); // 重新筛选和渲染
        }
    }
};
// 新增按钮
document.getElementById('addStatBonusBtn').onclick = function() {
    openStatBonusModal(false, null);
};

//堆叠工资柱状图
let statBonusStackedBarChart = null;

function renderStatBonusStackedBarChart() {
    const ctx = document.getElementById('statBonusStackedBarChart').getContext('2d');
    const labels = filteredStatRows.map(row => row.empName || row.empId);
    const baseSalaryData = filteredStatRows.map(row => Number(row.baseSalary) || 0);
    // 绩效奖金需重新计算，确保和表格一致
    const performanceBonusData = filteredStatRows.map(row => {
        const baseBonus = Number(row.baseBonus) || 0;
        const rewardFactor = Number(row.rewardFactor) || 0;
        return (baseBonus * rewardFactor).toFixed(2);
    });

    if (statBonusStackedBarChart) {
        statBonusStackedBarChart.destroy();
    }

    statBonusStackedBarChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '基本工资',
                    data: baseSalaryData,
                    backgroundColor: 'rgba(37,99,235,0.7)',
                    stack: '工资'
                },
                {
                    label: '绩效奖金',
                    data: performanceBonusData,
                    backgroundColor: 'rgba(245,158,11,0.8)',
                    stack: '工资'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            size: 16 // 图例字体大小
                        }
                    }
                },
                title: {
                    display: true,
                    text: '员工工资结构（堆叠柱状图）',
                    font: {
                        size: 20 // 标题字体大小
                    }
                }
            },
            scales: {
                x: {
                    stacked: true,
                    ticks: {
                        font: {
                            size: 14 // X轴字体大小
                        }
                    }
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    ticks: {
                        font: {
                            size: 14 // Y轴字体大小
                        }
                    }
                }
            }
        }
    });
}
// 流程任务操作
// 金额自动计算核心函数
function calculateTaskAmounts(taskId) {
    const task = processTaskSummary.find(t => t.id === taskId);
    if (!task) return;
    
    // 计算所有一级任务的金额
    task.level1Tasks.forEach(level1Task => {
        level1Task.amount = Math.round(task.contractAmount * level1Task.weight);
        
        // 计算该一级任务下所有二级任务的金额
        level1Task.level2Tasks.forEach(level2Task => {
            level2Task.amount = Math.round(level1Task.amount * level2Task.weight);
        });
    });
}

// 更新所有相关任务的金额显示
function updateTaskAmountsDisplay(taskId) {
    calculateTaskAmounts(taskId);
    
    // 重新渲染相关表格
    renderProcessTaskSummaryTable();
    
    // 如果当前有展开的一级任务，也要更新显示
    const expandedRow = document.querySelector(`tr[data-task-id="${taskId}"].expanded`);
    if (expandedRow) {
        renderLevel1Tasks(taskId);
        
        // 检查是否有展开的二级任务
        task.level1Tasks.forEach(level1Task => {
            const level1Row = document.querySelector(`tr[data-level1-id="${level1Task.id}"].expanded`);
            if (level1Row) {
                renderLevel2Tasks(taskId, level1Task.id);
            }
        });
    }
}


// 任务总表数据结构

let processTaskSummary = [
    {
        id: 1,
        contractNo: 'AA5634',
        orderNo: 'AAC3421',
        certType: '3C认证',
        taskNo: 'AACN2721',
        contractAmount: 12300,
        receivedAmount: 8450,
        organization: '京分中心',
        process: '认证一部',
        department: '2',
        taskStatus: '未封闭',
        workRatio: '56.89%',
        createTime: '2025/1/12',
        responsible: '王勇',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: [
                    {
                        id: 111,
                        taskName: '客户准护',
                        department: '分中心产品部',
                        weight: 0.1,
                        amount: 123, // 1230 * 0.1
                        progress: 100,
                        responsible: '王勇',
                        taskStatus: '已完成',
                        secondLevelRemark:''
                    },
                    {
                        id: 112,
                        taskName: '任务安排+流程跟踪',
                        department: '分中心检查部',
                        weight: 0.12,
                        amount: 147.6, // 1230 * 0.2
                        progress: 60,
                        responsible: '鲍新娟',
                        taskStatus: '已完成',
                        secondLevelRemark:''
                    },
                    {
                        id: 113,
                        taskName: '工厂检查',
                        department: '分中心检查部',
                        weight: 0.5,
                        amount: 615, // 1230 * 0.3
                        progress: 40,
                        responsible: '于兰',
                        taskStatus: '已完成',
                        secondLevelRemark:''
                    },
                    {
                        id: 114,
                        taskName: '工厂检查报告审核与认证',
                        department: '分中心检查部',
                        weight: 0.15,
                        amount: 184.5, // 1230 * 0.4
                        progress: 35,
                        responsible: '鲍新娟',
                        taskStatus: '已完成',
                        secondLevelRemark:''
                    },
                    {
                        id: 115,
                        taskName: '证书处理',
                        department: '分中心检查部',
                        weight: 0.05,
                        amount: 61.5, // 1230 * 0.4
                        progress: 30,
                        responsible: '鲍新娟',
                        taskStatus: '已完成',
                        secondLevelRemark:''
                    },
                    {
                        id: 116,
                        taskName: '资料归档',
                        department: '分中心检查部',
                        weight: 0.08,
                        amount: 98.4, // 1230 * 0.4
                        progress: 25,
                        responsible: '鲍新娟',
                        taskStatus: '已完成',
                        secondLevelRemark:''
                    },
                ]
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 2,
        contractNo: 'AA7637',
        orderNo: 'AAC6541',
        certType: '3C认证',
        taskNo: 'AACN2711',
        contractAmount: 34200,
        receivedAmount: 29000,
        organization: '京分中心',
        process: '认证一部',
        department: '1',
        taskStatus: '未封闭',
        workRatio: '67.87%',
        createTime: '2025/1/13',
        responsible: '李方',
        level1Tasks: [
            {
                id: 11,
                taskName: '客户维护',
                department: '分中心产品部',
                weight: 0.1,
                amount: 3420,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '任务安排+流程监控',
                department: '分中心检查部',
                weight: 0.12,
                amount: 4104,
                progress: 60,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '工厂检查',
                department: '分中心检查员',
                weight: 0.5,
                amount: 17100,
                progress: 40,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '工厂检查报告审核与认证',
                department: '分中心检查部',
                weight: 0.15,
                amount: 5130,
                progress: 35,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '证书处理',
                department: '分中心检查部',
                weight: 0.05,
                amount: 1710,
                progress: 30,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '资料归档',
                department: '分中心检查部',
                weight: 0.08,
                amount: 2736,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 3,
        contractNo: 'AB7654',
        orderNo: 'ABC6653',
        certType: 'Ⅱ型认证',
        taskNo: 'AACN6653',
        contractAmount: 44000,
        receivedAmount: 40035,
        organization: '南京分中心',
        process: '认证一部',
        department: '1',
        taskStatus: '未封闭',
        workRatio: '95.67%',
        createTime: '2025/1/14',
        responsible: '王素秋',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '项目评价部',
                weight: 0.1,
                amount: 4400,
                progress: 100,
                responsible: '陈澄',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '合同签订+项目管理',
                department: '项目评价部',
                weight: 0.1,
                amount: 4400,
                progress: 60,
                responsible: '陈澄',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '项目实施',
                department: '项目评价部',
                weight: 0.4,
                amount: 17600,
                progress: 40,
                responsible: '陈澄',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '项目审核与评价',
                department: '绿发部',
                weight: 0.4,
                amount: 17600,
                progress: 35,
                responsible: '陆新伟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 4,
        contractNo: 'AC1712',
        orderNo: 'ACC2324',
        certType: '自愿认证',
        taskNo: 'ACCN2867',
        contractAmount: 14550,
        receivedAmount: 14550,
        organization: '南京分中心',
        process: '认证三部',
        department: '2',
        taskStatus: '已封闭',
        workRatio: '98.74%',
        createTime: '2025/1/14',
        responsible: '钱斌',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 5,
        contractNo: 'BA1345',
        orderNo: 'BAN2453',
        certType: '3C认证',
        taskNo: 'BAN2453',
        contractAmount: 8000,
        receivedAmount: 8000,
        organization: '南京分中心',
        process: '认证三部',
        department: '1',
        taskStatus: '已封闭',
        workRatio: '100%',
        createTime: '2025/1/14',
        responsible: '钱斌',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 6,
        contractNo: 'BB7625',
        orderNo: 'BBN2455',
        certType: 'Ⅱ型认证',
        taskNo: 'BBN2453',
        contractAmount: 15450,
        receivedAmount: 12050,
        organization: '南京分中心',
        process: '认证四部',
        department: '3',
        taskStatus: '未封闭',
        workRatio: '45.45%',
        createTime: '2025/1/15',
        responsible: '郑师容',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 7,
        contractNo: 'BB7626',
        orderNo: 'BBN2456',
        certType: 'Ⅱ型认证',
        taskNo: 'BBN2454',
        contractAmount: 18800,
        receivedAmount: 13540,
        organization: '南京分中心',
        process: '认证四部',
        department: '3',
        taskStatus: '未封闭',
        workRatio: '34.74%',
        createTime: '2025/1/15',
        responsible: '郑师容',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 8,
        contractNo: 'BC6074',
        orderNo: 'BCN6674',
        certType: '自愿认证',
        taskNo: 'BCN66743',
        contractAmount: 10000,
        receivedAmount: 10000,
        organization: '南京分中心',
        process: '认证五部',
        department: '1',
        taskStatus: '未封闭',
        workRatio: '36.70%',
        createTime: '2025/1/16',
        responsible: '张起灵',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 9,
        contractNo: 'BC6075',
        orderNo: 'BCN6675',
        certType: '自愿认证',
        taskNo: 'BCN66744',
        contractAmount: 15660,
        receivedAmount: 12330,
        organization: '南京分中心',
        process: '认证五部',
        department: '1',
        taskStatus: '未封闭',
        workRatio: '23.76%',
        createTime: '2025/1/27',
        responsible: '张起灵',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 10,
        contractNo: 'CA1267',
        orderNo: 'CAS1267',
        certType: '技术服务',
        taskNo: 'CAS12671',
        contractAmount: 22005,
        receivedAmount: 15150,
        organization: '南京分中心',
        process: '认证三部',
        department: '2',
        taskStatus: '未封闭',
        workRatio: '33.55%',
        createTime: '2025/1/28',
        responsible: '荣海',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 11,
        contractNo: 'CB0017',
        orderNo: 'CBN1727',
        certType: '技术服务',
        taskNo: 'CBN12774',
        contractAmount: 12300,
        receivedAmount: 12300,
        organization: '上海分中心',
        process: '认证四部',
        department: '3',
        taskStatus: '未封闭',
        workRatio: '45.45%',
        createTime: '2025/1/29',
        responsible: '郑师容',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 12,
        contractNo: 'DA1721',
        orderNo: 'DAN6673',
        certType: '项目评价',
        taskNo: 'DAN667311',
        contractAmount: 22400,
        receivedAmount: 22400,
        organization: '南京分中心',
        process: '认证五部',
        department: '1',
        taskStatus: '未封闭',
        workRatio: '67.73%',
        createTime: '2025/1/30',
        responsible: '黄琴',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 13,
        contractNo: 'DB7766',
        orderNo: 'DBQ3327',
        certType: '项目评价',
        taskNo: 'DBQ332710',
        contractAmount: 30400,
        receivedAmount: 2025,
        organization: '青岛分中心',
        process: '认证五部',
        department: '1',
        taskStatus: '未封闭',
        workRatio: '80.22%',
        createTime: '2025/2/3',
        responsible: '黄琴',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 14,
        contractNo: 'EA1886',
        orderNo: 'EAN188601',
        certType: '绿色技术服务',
        taskNo: 'WAN188601',
        contractAmount: 44075,
        receivedAmount: 20245,
        organization: '南京分中心',
        process: '认证三部',
        department: '2',
        taskStatus: '未封闭',
        workRatio: '22.53%',
        createTime: '2025/2/4',
        responsible: '钱斌',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    },
    {
        id: 15,
        contractNo: 'EF6673',
        orderNo: 'EFN33656',
        certType: '绿色技术服务',
        taskNo: 'EFN33656124',
        contractAmount: 33600,
        receivedAmount: 33600,
        organization: '南京分中心',
        process: '认证四部',
        department: '1',
        taskStatus: '未封闭',
        workRatio: '33.33%',
        createTime: '2025/2/4',
        responsible: '郑师容',
        level1Tasks: [
            {
                id: 11,
                taskName: '市场开发',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 100,
                responsible: '王勇',
                taskStatus: '已完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 12,
                taskName: '证书服务',
                department: '分中心产品部',
                weight: 0.1,
                amount: 1230,
                progress: 60,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 13,
                taskName: '认证受理、下达检测任务',
                department: '分中心产品部',
                weight: 0.03,
                amount: 369,
                progress: 40,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 14,
                taskName: '认证资料处理及归档',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 35,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 15,
                taskName: '任务下达',
                department: '总部产品部',
                weight: 0.02,
                amount: 246,
                progress: 30,
                responsible: '钱奇兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 16,
                taskName: '工厂检查',
                department: '分中心检查部',
                weight: 0.03,
                amount: 369,
                progress: 25,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 17,
                taskName: '检查报告审核加评定',
                department: '分中心检查员',
                weight: 0.03,
                amount: 4305,
                progress: 20,
                responsible: '于兰',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 18,
                taskName: '认证初评与收费下达',
                department: '分中心检查部',
                weight: 0.15,
                amount: 1845,
                progress: 15,
                responsible: '鲍新娟',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 19,
                taskName: '认证复核及认证决定',
                department: '总部产品部',
                weight: 0.1,
                amount: 1230,
                progress: 10,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 20,
                taskName: '交费确认、客户发票管理',
                department: '总部产品部',
                weight: 0.05,
                amount: 615,
                progress: 5,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 21,
                taskName: '自我声明系统上报',
                department: '总部产品部',
                weight: 0.03,
                amount: 369,
                progress: 0,
                responsible: '刘秋华',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            },
            {
                id: 22,
                taskName: '客户认证后维护',
                department: '分中心产品部',
                weight: 0.02,
                amount: 246,
                progress: 10,
                responsible: '王勇',
                taskStatus: '未完成',
                firstLevelRemark:'',
                level2Tasks: []
            }
        ]
    }
];

// 渲染任务总表
function renderProcessTaskSummaryTable() {
    const tbody = document.getElementById('processTaskSummaryTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    processTaskSummary.forEach(task => {
        const tr = document.createElement('tr');
        tr.className = 'main-task-row';
        tr.setAttribute('data-task-id', task.id);
        tr.innerHTML = `
            <td>
                <button class="expand-btn" onclick="toggleLevel1Tasks(${task.id}, this)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </td>
            <td>${task.contractNo}</td>
            <td>${task.orderNo}</td>
            <td>${task.certType}</td>
            <td>${task.taskNo}</td>
            <td>¥${task.contractAmount.toLocaleString()}</td>
            <td>¥${task.receivedAmount.toLocaleString()}</td>
            <td>${task.organization}</td>
            <td>${task.process}</td>
            <td>${task.department}</td>
            <td>${task.taskStatus}</td>
            <td>${task.workRatio}</td>
            <td>${task.createTime}</td>
            <td>${task.responsible}</td>
            <td class="action-cell">
                <button class="action-btn edit-btn" onclick="editProcessTask(${task.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="action-btn delete-btn" onclick="deleteProcessTask(${task.id})">
                    <i class="fas fa-trash-alt"></i> 删除
                </button>
            </td>
        `;
        tbody.appendChild(tr);

        // 添加一级流程任务行（初始隐藏）
        const level1Row = document.createElement('tr');
        level1Row.className = 'first-level-container';
        level1Row.id = `firstLevel_${task.id}`;
        level1Row.style.display = 'none';
        level1Row.innerHTML = `
            <td colspan="15">
                <div class="sub-table-container">
                    <div class="sub-table-header">
                        <h4>一级流程任务</h4>
                        <div>
                            <button class="btn btn-sm btn-success" onclick="addLevel1Task(${task.id})">
                                <i class="fas fa-plus"></i> 新增一级流程
                            </button>
                        </div>
                    </div>
                    <table class="sub-table first-level-table">
                        <thead>
                            <tr>
                                <th width="30px"></th>
                                <th>流程编号</th>
                                <th>任务名称</th>
                                <th>负责部门</th>
                                <th>权重</th>
                                <th>金额</th>
                                <th>完成率</th>
                                <th>责任人</th>
                                <th>任务完成情况</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="firstLevelBody_${task.id}">
                            <!-- 一级任务数据 -->
                        </tbody>
                    </table>
                </div>
            </td>
        `;
        tbody.appendChild(level1Row);
    });
}
// 展开/收起一级任务
function toggleLevel1Tasks(taskId, btn) {
    const level1Row = document.getElementById(`firstLevel_${taskId}`);
    const icon = btn.querySelector('i');

    if (level1Row.style.display === 'none') {
        level1Row.style.display = '';
        icon.className = 'fas fa-chevron-down';
        btn.classList.add('expanded');
        // 渲染一级任务数据
        renderLevel1Tasks(taskId);
    } else {
        level1Row.style.display = 'none';
        icon.className = 'fas fa-chevron-right';
        btn.classList.remove('expanded');
    }
}

// 渲染一级任务
function renderLevel1Tasks(taskId) {
    const task = processTaskSummary.find(t => t.id === taskId);
    if (!task) return;

    const tbody = document.getElementById(`firstLevelBody_${taskId}`);
    tbody.innerHTML = '';

    task.level1Tasks.forEach((level1Task, index) => {
        const tr = document.createElement('tr');
        tr.className = 'first-level-row';
        tr.setAttribute('data-first-level-id', `${taskId}_${level1Task.id}`);
        tr.innerHTML = `
            <td>
                <button class="expand-btn" onclick="toggleLevel2Tasks('${taskId}_${level1Task.id}', this)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </td>
            <td>${index + 1}</td>
            <td>${level1Task.taskName}</td>
            <td>${level1Task.department}</td>
            <td>${(level1Task.weight * 100).toFixed(1)}%</td>
            <td>¥${level1Task.amount.toLocaleString()}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress" style="width: ${level1Task.progress}%" data-progress="${level1Task.progress}"></div>
                    </div>
                    <span class="progress-text">${level1Task.progress}%</span>
                </div>
            </td>
            <td>${level1Task.responsible}</td>
            <td>${level1Task.taskStatus}</td>
            <td>${level1Task.firstLevelRemark}</td>
            <td class="action-cell">
                <button class="action-btn edit-btn" onclick="editLevel1Task(${taskId}, ${level1Task.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="action-btn delete-btn" onclick="deleteLevel1Task(${taskId}, ${level1Task.id})">
                    <i class="fas fa-trash-alt"></i> 删除
                </button>
            </td>
        `;
        tbody.appendChild(tr);

        // 添加二级任务行（初始隐藏）
        const level2Row = document.createElement('tr');
        level2Row.className = 'second-level-container';
        level2Row.id = `secondLevel_${taskId}_${level1Task.id}`;
        level2Row.style.display = 'none';
        level2Row.innerHTML = `
            <td colspan="10">
                <div class="sub-table-container level-2">
                    <div class="sub-table-header">
                        <h5>二级流程任务</h5>
                        <div>
                            <button class="btn btn-sm btn-success" onclick="addLevel2Task(${taskId}, ${level1Task.id})">
                                <i class="fas fa-plus"></i> 新增二级流程
                            </button>
                        </div>
                    </div>
                    <table class="sub-table second-level-table">
                        <thead>
                            <tr>
                                <th>流程编号</th>
                                <th>任务名称</th>
                                <th>负责部门</th>
                                <th>产值权重</th>
                                <th>金额</th>
                                <th>完成率</th>
                                <th>责任人</th>
                                <th>任务完成情况</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="secondLevelBody_${taskId}_${level1Task.id}">
                            <!-- 二级任务数据 -->
                        </tbody>
                    </table>
                </div>
            </td>
        `;
        tbody.appendChild(level2Row);
    });
}

// 删除流程任务函数
function deleteProcessTask(taskId) {
    if (!confirm('确定要删除该任务及其所有子任务吗？')) return;

    const index = processTaskSummary.findIndex(t => t.id === taskId);
    if (index !== -1) {
        processTaskSummary.splice(index, 1);
        renderProcessTaskSummaryTable();
    }
}

// 展开/收起二级任务
function toggleLevel2Tasks(firstLevelId, btn) {
    const level2Row = document.getElementById(`secondLevel_${firstLevelId}`);
    const icon = btn.querySelector('i');

    if (level2Row.style.display === 'none') {
        level2Row.style.display = '';
        icon.className = 'fas fa-chevron-down';
        btn.classList.add('expanded');
        // 渲染二级任务数据
        renderLevel2Tasks(firstLevelId);
    } else {
        level2Row.style.display = 'none';
        icon.className = 'fas fa-chevron-right';
        btn.classList.remove('expanded');
    }
}

// 渲染二级任务
function renderLevel2Tasks(firstLevelId) {
    // 解析firstLevelId，格式为 "taskId_level1TaskId"
    const [taskId, level1TaskId] = firstLevelId.split('_').map(id => parseInt(id));

    const task = processTaskSummary.find(t => t.id === taskId);
    if (!task) return;

    const level1Task = task.level1Tasks.find(t => t.id === level1TaskId);
    if (!level1Task) return;

    const tbody = document.getElementById(`secondLevelBody_${firstLevelId}`);
    tbody.innerHTML = '';

    level1Task.level2Tasks.forEach((level2Task, index) => {
        const tr = document.createElement('tr');
        tr.setAttribute('data-second-level-id', `${firstLevelId}_${level2Task.id}`);
        tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${level2Task.taskName}</td>
            <td>${level2Task.department}</td> 
            <td>${(level2Task.weight * 100).toFixed(1)}%</td>
            <td>¥${level2Task.amount.toLocaleString()}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress" style="width: ${level2Task.progress}%" data-progress="${level2Task.progress}"></div>
                    </div>
                    <span class="progress-text">${level2Task.progress}%</span>
                </div>
            </td>
            <td>${level2Task.responsible}</td>
            <td>${level2Task.taskStatus}</td>
            <td>${level2Task.secondLevelRemark}</td>
            <td class="action-cell">
                <button class="action-btn edit-btn" onclick="editLevel2Task(${taskId}, ${level1TaskId}, ${level2Task.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="action-btn delete-btn" onclick="deleteLevel2Task(${taskId}, ${level1TaskId}, ${level2Task.id})">
                    <i class="fas fa-trash-alt"></i> 删除
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

function updateStepProgress(taskId, stepIdx, level) {
    openStepProgressModal(taskId, stepIdx, level);
}

// 编辑任务总表任务
function editProcessTask(taskId) {
    const task = processTaskSummary.find(t => t.id === taskId);
    if (!task) return;

    // 打开编辑模态框
    openProcessTaskModal(true, taskId);
}

// 新增一级任务
function addLevel1Task(taskId) {
    const task = processTaskSummary.find(t => t.id === taskId);
    if (!task) return;

    // 打开新增一级流程模态框
    openFirstLevelProcessModal(false, taskId);
}

// 编辑一级任务
function editLevel1Task(taskId, level1TaskId) {
    // 打开编辑一级流程模态框
    openFirstLevelProcessModal(true, taskId, level1TaskId);
}

// 删除一级任务
function deleteLevel1Task(taskId, level1TaskId) {
    if (!confirm('确定要删除该一级任务及其所有子任务吗？')) return;

    const task = processTaskSummary.find(t => t.id === taskId);
    if (!task) return;

    const index = task.level1Tasks.findIndex(t => t.id === level1TaskId);
    if (index !== -1) {
        task.level1Tasks.splice(index, 1);
        renderLevel1Tasks(taskId);
    }
}

// 新增二级任务
function addLevel2Task(taskId, level1TaskId) {
    // 打开新增二级流程模态框
    openSecondLevelProcessModal(false, taskId, level1TaskId);
}

// 编辑二级任务
function editLevel2Task(taskId, level1TaskId, level2TaskId) {
    // 打开编辑二级流程模态框
    openSecondLevelProcessModal(true, taskId, level1TaskId, level2TaskId);
}

// 删除二级任务
function deleteLevel2Task(taskId, level1TaskId, level2TaskId) {
    if (!confirm('确定要删除该二级任务吗？')) return;

    const task = processTaskSummary.find(t => t.id === taskId);
    if (!task) return;

    const level1Task = task.level1Tasks.find(t => t.id === level1TaskId);
    if (!level1Task) return;

    const index = level1Task.level2Tasks.findIndex(t => t.id === level2TaskId);
    if (index !== -1) {
        level1Task.level2Tasks.splice(index, 1);
        renderLevel2Tasks(taskId, level1TaskId);
    }
}

// 页面初始化渲染
function renderAllProcessTasks() {
    renderProcessTaskSummaryTable();
}
let currentStepTaskId = null;
let currentStepLevel = null;
let currentStepIdx = null;
let isEditStep = false;

// // 打开新增任务模态框
// window.openAddStepModal = function(taskId, level) {
//   currentStepTaskId = taskId;
//   currentStepLevel = level;
//   isEditStep = false;
//   document.getElementById('stepModalTitle').innerText = '新增任务';
//   document.getElementById('stepName').value = '';
//   document.getElementById('stepProgress').value = '';
//   document.getElementById('stepModal').style.display = 'flex';
//   document.getElementById('saveStepBtn').onclick = saveStep;
// };

// // 打开进度编辑模态框
// window.openStepProgressModal = function(taskId, stepIdx, level) {
//   currentStepTaskId = taskId;
//   currentStepLevel = level;
//   currentStepIdx = stepIdx;
//   isEditStep = true;
//   // 找到当前任务
//   const task = findProcessTaskById(processTasks, taskId);
//   if (task && task.steps[stepIdx]) {
//     document.getElementById('stepModalTitle').innerText = '编辑任务步骤';
//     document.getElementById('stepName').value = task.steps[stepIdx].name;
//     document.getElementById('stepProgress').value = task.steps[stepIdx].progress;
//     document.getElementById('stepModal').style.display = 'flex';
//     document.getElementById('saveStepBtn').onclick = saveStep;
//     document.getElementById('deleteStepBtn').style.display = '';
//     document.getElementById('deleteStepBtn').onclick = deleteStep;
//   }
// };

// function closeStepModal() {
//   document.getElementById('stepModal').style.display = 'none';
// }

// // 保存步骤
// function saveStep() {
//   const name = document.getElementById('stepName').value.trim();
//   const progress = Math.max(0, Math.min(100, parseInt(document.getElementById('stepProgress').value) || 0));
//   if (!name) {
//     alert('请输入任务名称');
//     return;
//   }
//   // 找到当前任务
//   const task = findProcessTaskById(processTasks, currentStepTaskId);
//   if (task) {
//     if (isEditStep && typeof currentStepIdx === 'number') {
//       task.steps[currentStepIdx].name = name;
//       task.steps[currentStepIdx].progress = progress;
//     } else {
//       task.steps.push({ name, progress });
//     }
//     closeStepModal();
//     renderAllProcessTasks();
//   }
// }
// // 删除步骤
// function deleteStep() {
//   if (!confirm('确定要删除该任务步骤吗？')) return;
//   const task = findProcessTaskById(processTasks, currentStepTaskId);
//   if (task && typeof currentStepIdx === 'number') {
//     task.steps.splice(currentStepIdx, 1);
//     closeStepModal();
//     renderAllProcessTasks();
//   }
// }
// function closeStepModal() {
//   document.getElementById('stepModal').style.display = 'none';
// }

// // 递归查找任务
// function findProcessTaskById(tasks, id) {
//   for (let t of tasks) {
//     if (t.id === id) return t;
//     if (t.children && t.children.length) {
//       const found = findProcessTaskById(t.children, id);
//       if (found) return found;
//     }
//   }
//   return null;
// }
//新增大流程
// 打开新增流程任务模态框
// function openAddProcessTaskModal() {
//   document.getElementById('processTaskModalTitle').innerText = '新增流程任务';
//   document.getElementById('processTaskLevel').value = '1';
//   document.getElementById('processTaskCode').value = '';
//   document.getElementById('processTaskContent').value = '';
//   document.getElementById('processTaskType').value = '';
//   document.getElementById('processTaskSubsidy').value = '';
//   document.getElementById('processTaskAmount').value = '';
//   document.getElementById('processTaskParentRow').style.display = 'none';
//   document.getElementById('processTaskModal').style.display = 'flex';
//   updateProcessTaskParentOptions();

//   // 级别切换时显示父级选择
//   document.getElementById('processTaskLevel').onchange = function() {
//     updateProcessTaskParentOptions();
//   };
// }

// // 关闭
// function closeProcessTaskModal() {
//   document.getElementById('processTaskModal').style.display = 'none';
// }

// // 更新父级任务下拉
// function updateProcessTaskParentOptions() {
//   const level = document.getElementById('processTaskLevel').value;
//   const parentRow = document.getElementById('processTaskParentRow');
//   const parentSelect = document.getElementById('processTaskParent');
//   parentSelect.innerHTML = '';
//   if (level === '2') {
//     parentRow.style.display = '';
//     // 一级流程
//     processTasks.forEach(t => {
//       parentSelect.innerHTML += `<option value="${t.id}">${t.code} - ${t.content}</option>`;
//     });
//   } else if (level === '3') {
//     parentRow.style.display = '';
//     // 二级流程
//     processTasks.forEach(t => {
//       (t.children || []).forEach(c => {
//         parentSelect.innerHTML += `<option value="${c.id}">${c.code} - ${c.content}</option>`;
//       });
//     });
//   } else {
//     parentRow.style.display = 'none';
//   }
// }

// // 流程任务模态框相关函数
// let editingProcessTaskId = null;

// 打开流程任务模态框
function openProcessTaskModal(isEdit = false, taskId = null) {
    editingProcessTaskId = isEdit ? taskId : null;

    const modal = document.getElementById('processTaskModal');
    const title = document.getElementById('processTaskModalTitle');

    if (isEdit && taskId) {
        const task = processTaskSummary.find(t => t.id === taskId);
        if (!task) return;

        title.textContent = '编辑流程任务';
        document.getElementById('processContractId').value = task.contractNo;
        document.getElementById('processOrderId').value = task.orderNo;
        document.getElementById('processCertType').value = task.certType;
        document.getElementById('processTaskId').value = task.taskNo;
        document.getElementById('processContractAmount').value = task.contractAmount;
        document.getElementById('processReceivedAmount').value = task.receivedAmount;
        document.getElementById('processOrganization').value = task.organization;
        document.getElementById('processFlow').value = task.process;
        document.getElementById('processGroup').value = task.department;
        document.getElementById('processTaskStatus').value = task.taskStatus;
        document.getElementById('processWorkRatio').value = task.workRatio;
        document.getElementById('processCreateTime').value = task.createTime;
        document.getElementById('processResponsible').value = task.responsible;
    } else {
        title.textContent = '新增流程任务';
        // 清空表单
        document.querySelectorAll('#processTaskModal input, #processTaskModal select').forEach(input => {
            input.value = '';
        });
        document.getElementById('processTaskStatus').value = '未封闭';
        // document.getElementById('processCreateTime').value = new Date().toISOString().split('T')[0];
    }

    modal.style.display = 'flex';
}

// 关闭流程任务模态框
function closeProcessTaskModal() {
    document.getElementById('processTaskModal').style.display = 'none';
    editingProcessTaskId = null;
}

// 保存流程任务
function saveProcessTask() {
    const contractNo = document.getElementById('processContractId').value.trim();
    const orderNo = document.getElementById('processOrderId').value.trim();
    const certType = document.getElementById('processCertType').value.trim();
    const taskNo = document.getElementById('processTaskId').value.trim();
    const contractAmount = parseFloat(document.getElementById('processContractAmount').value) || 0;
    const receivedAmount = parseFloat(document.getElementById('processReceivedAmount').value) || 0;
    const organization = document.getElementById('processOrganization').value.trim();
    const process = document.getElementById('processFlow').value.trim();
    const department = document.getElementById('processGroup').value.trim();
    const taskStatus = document.getElementById('processTaskStatus').value;
    const workRatio = document.getElementById('processWorkRatio').value.trim();
    const createTime = document.getElementById('processCreateTime').value.trim();
    const responsible = document.getElementById('processResponsible').value.trim();

    if (!contractNo || !orderNo || !taskNo) {
        alert('请填写必要信息');
        return;
    }

    const taskData = {
        contractNo,
        orderNo,
        certType,
        taskNo,
        contractAmount,
        receivedAmount,
        organization,
        process,
        department,
        taskStatus,
        workRatio,
        createTime,
        responsible
    };

    if (editingProcessTaskId) {
        // 编辑模式
        const task = processTaskSummary.find(t => t.id === editingProcessTaskId);
        if (task) {
            Object.assign(task, taskData);
        }
    } else {
        // 新增模式
        taskData.id = Date.now();
        taskData.level1Tasks = [];
        processTaskSummary.push(taskData);
    }

    closeProcessTaskModal();
    renderProcessTaskSummaryTable();
}

// 绑定保存按钮事件
document.getElementById('saveProcessTaskBtn').onclick = saveProcessTask;
// 优化自动计算设置函数
function setupAutoCalculation() {
    // 一级流程权重变化时显示预计算金额
    const firstLevelWeight = document.getElementById('firstLevelWeight');
    const firstLevelAmount = document.getElementById('firstLevelAmount');
    
    if (firstLevelWeight && firstLevelAmount) {
        firstLevelWeight.addEventListener('input', function() {
            const task = processTaskSummary.find(t => t.id === editingFirstLevelParentTaskId);
            if (task) {
                const weightStr = this.value.trim();
                let weight = 0;
                if (weightStr.includes('%')) {
                    weight = parseFloat(weightStr.replace('%', '')) / 100;
                } else {
                    weight = parseFloat(weightStr) || 0;
                }
                const calculatedAmount = Math.round(task.contractAmount * weight);
                firstLevelAmount.value = calculatedAmount;
                firstLevelAmount.style.backgroundColor = '#e8f5e8'; // 提示这是自动计算的
            }
        });
        
        // 禁用金额输入框，因为它是自动计算的
        firstLevelAmount.readOnly = true;
        firstLevelAmount.style.backgroundColor = '#f5f5f5';
    }

    // 二级流程权重变化时显示预计算金额
    const secondLevelWeight = document.getElementById('secondLevelValueWeight');
    const secondLevelAmount = document.getElementById('secondLevelAmount');
    
    if (secondLevelWeight && secondLevelAmount) {
        secondLevelWeight.addEventListener('input', function() {
            const task = processTaskSummary.find(t => t.id === editingSecondLevelParentTaskId);
            if (task) {
                const level1Task = task.level1Tasks.find(t => t.id === editingSecondLevelLevel1TaskId);
                if (level1Task) {
                    const weightStr = this.value.trim();
                    let weight = 0;
                    if (weightStr.includes('%')) {
                        weight = parseFloat(weightStr.replace('%', '')) / 100;
                    } else {
                        weight = parseFloat(weightStr) || 0;
                    }
                    const calculatedAmount = Math.round(level1Task.amount * weight);
                    secondLevelAmount.value = calculatedAmount;
                    secondLevelAmount.style.backgroundColor = '#e8f5e8'; // 提示这是自动计算的
                }
            }
        });
        
        // 禁用金额输入框，因为它是自动计算的
        secondLevelAmount.readOnly = true;
        secondLevelAmount.style.backgroundColor = '#f5f5f5';
    }
}
// 一级流程任务模态框相关函数
let editingFirstLevelTaskId = null;
let editingFirstLevelParentTaskId = null;

// 打开一级流程任务模态框
function openFirstLevelProcessModal(isEdit = false, taskId = null, level1TaskId = null) {
    editingFirstLevelTaskId = isEdit ? level1TaskId : null;
    editingFirstLevelParentTaskId = taskId;

    const modal = document.getElementById('firstLevelProcessModal');
    const title = document.getElementById('firstLevelProcessModalTitle');

    if (isEdit && taskId && level1TaskId) {
        const task = processTaskSummary.find(t => t.id === taskId);
        if (!task) return;

        const level1Task = task.level1Tasks.find(t => t.id === level1TaskId);
        if (!level1Task) return;

        title.textContent = '编辑一级流程';
        document.getElementById('firstLevelProcessId').value = level1Task.id;
        document.getElementById('firstLevelTaskName').value = level1Task.taskName;
        document.getElementById('firstLevelDepartment').value = level1Task.department;
        document.getElementById('firstLevelWeight').value = (level1Task.weight * 100).toFixed(1) + '%';
        document.getElementById('firstLevelAmount').value = level1Task.amount;
        document.getElementById('firstLevelCompletionRate').value = level1Task.progress;
        document.getElementById('firstLevelProgressText').textContent = level1Task.progress + '%';
        document.getElementById('firstLevelCompletionStatus').value = level1Task.taskStatus;
        document.getElementById('firstLevelResponsible').value = level1Task.responsible;
        document.getElementById('firstLevelRemark').value = level1Task.firstLevelRemark;
    } else {
        title.textContent = '新增一级流程';
        // 清空表单
        document.querySelectorAll('#firstLevelProcessModal input, #firstLevelProcessModal select, #firstLevelProcessModal textarea').forEach(input => {
            if (input.type === 'range') {
                input.value = '0';
            } else {
                input.value = '';
            }
        });
        document.getElementById('firstLevelProgressText').textContent = '0%';
        document.getElementById('firstLevelCompletionStatus').value = '未完成';

        // 设置默认责任人
        if (taskId) {
            const task = processTaskSummary.find(t => t.id === taskId);
            if (task) {
                document.getElementById('firstLevelResponsible').value = task.responsible;
            }
        }
    }

    modal.style.display = 'flex';
    
    // 设置自动计算
    setTimeout(() => {
        setupAutoCalculation();
    }, 100);
}

// 关闭一级流程任务模态框
function closeFirstLevelProcessModal() {
    document.getElementById('firstLevelProcessModal').style.display = 'none';
    editingFirstLevelTaskId = null;
    editingFirstLevelParentTaskId = null;
}

// 更新一级流程进度显示
function updateFirstLevelProgress(value) {
    document.getElementById('firstLevelProgressText').textContent = value + '%';
}

// 保存一级流程任务
// function saveFirstLevelProcess() {
//     const taskName = document.getElementById('firstLevelTaskName').value.trim();
//     const weightStr = document.getElementById('firstLevelWeight').value.trim();
//     const amount = parseFloat(document.getElementById('firstLevelAmount').value) || 0;
//     const progress = parseInt(document.getElementById('firstLevelCompletionRate').value) || 0;
//     const taskStatus = document.getElementById('firstLevelCompletionStatus').value;
//     const responsible = document.getElementById('firstLevelResponsible').value.trim();
//     const firstLevelRemark = document.getElementById('firstLevelRemark').value.trim();
    
//     if (!taskName || !responsible) {
//         alert('请填写任务名称和责任人');
//         return;
//     }

//     // 解析权重
//     let weight = 0;
//     if (weightStr.includes('%')) {
//         weight = parseFloat(weightStr.replace('%', '')) / 100;
//     } else {
//         weight = parseFloat(weightStr) || 0;
//     }

//     const task = processTaskSummary.find(t => t.id === editingFirstLevelParentTaskId);
//     if (!task) return;

//     // 自动计算金额：任务金额 × 权重
//     const calculatedAmount = Math.round(task.contractAmount * weight);

//     const taskData = {
//         taskName,
//         weight,
//         amount: calculatedAmount, // 使用计算出的金额
//         progress,
//         responsible,
//         taskStatus,
//         firstLevelRemark,
//         level2Tasks: editingFirstLevelTaskId ? 
//             task.level1Tasks.find(t => t.id === editingFirstLevelTaskId)?.level2Tasks || [] : []
//     };

//     if (editingFirstLevelTaskId) {
//         // 编辑模式
//         const level1Task = task.level1Tasks.find(t => t.id === editingFirstLevelTaskId);
//         if (level1Task) {
//             Object.assign(level1Task, taskData);
//         }
//     } else {
//         // 新增模式
//         taskData.id = Date.now();
//         task.level1Tasks.push(taskData);
//     }

//     closeFirstLevelProcessModal();
//     // 修复：正确调用渲染函数
//     renderLevel1Tasks(editingFirstLevelParentTaskId);
//     // 同时更新主表显示
//     renderProcessTaskSummaryTable();
// }
// 修改保存一级流程函数，确保权重变化时重新计算
function saveFirstLevelProcess() {
    const taskName = document.getElementById('firstLevelTaskName').value.trim();
    const department = document.getElementById('firstLevelDepartment').value.trim();
    const weightStr = document.getElementById('firstLevelWeight').value.trim();
    const progress = parseInt(document.getElementById('firstLevelCompletionRate').value) || 0;
    const taskStatus = document.getElementById('firstLevelCompletionStatus').value;
    const responsible = document.getElementById('firstLevelResponsible').value.trim();
    const firstLevelRemark = document.getElementById('firstLevelRemark').value.trim();
    
    if (!taskName || !responsible) {
        alert('请填写任务名称和责任人');
        return;
    }

    // 解析权重
    let weight = 0;
    if (weightStr.includes('%')) {
        weight = parseFloat(weightStr.replace('%', '')) / 100;
    } else {
        weight = parseFloat(weightStr) || 0;
    }

    const task = processTaskSummary.find(t => t.id === editingFirstLevelParentTaskId);
    if (!task) return;

    const taskData = {
        taskName,
        weight,
        amount: 0, // 将通过calculateTaskAmounts计算
        progress,
        responsible,
        taskStatus,
        firstLevelRemark,
        level2Tasks: editingFirstLevelTaskId ? 
            task.level1Tasks.find(t => t.id === editingFirstLevelTaskId)?.level2Tasks || [] : []
    };

    if (editingFirstLevelTaskId) {
        // 编辑模式
        const level1Task = task.level1Tasks.find(t => t.id === editingFirstLevelTaskId);
        if (level1Task) {
            Object.assign(level1Task, taskData);
        }
    } else {
        // 新增模式
        taskData.id = Date.now();
        task.level1Tasks.push(taskData);
    }

    // 重新计算所有金额
    updateTaskAmountsDisplay(editingFirstLevelParentTaskId);
    
    closeFirstLevelProcessModal();
}
// 绑定保存按钮事件
document.getElementById('saveFirstLevelProcessBtn').onclick = saveFirstLevelProcess;

// 二级流程任务模态框相关函数
let editingSecondLevelTaskId = null;
let editingSecondLevelParentTaskId = null;
let editingSecondLevelLevel1TaskId = null;

// 打开二级流程任务模态框
function openSecondLevelProcessModal(isEdit = false, taskId = null, level1TaskId = null, level2TaskId = null) {
    editingSecondLevelTaskId = isEdit ? level2TaskId : null;
    editingSecondLevelParentTaskId = taskId;
    editingSecondLevelLevel1TaskId = level1TaskId;

    const modal = document.getElementById('secondLevelProcessModal');
    const title = document.getElementById('secondLevelProcessModalTitle');

    if (isEdit && taskId && level1TaskId && level2TaskId) {
        const task = processTaskSummary.find(t => t.id === taskId);
        if (!task) return;

        const level1Task = task.level1Tasks.find(t => t.id === level1TaskId);
        if (!level1Task) return;

        const level2Task = level1Task.level2Tasks.find(t => t.id === level2TaskId);
        if (!level2Task) return;

        title.textContent = '编辑二级流程';
        document.getElementById('secondLevelProcessId').value = level2Task.id;
        document.getElementById('secondLevelTaskName').value = level2Task.taskName;
        document.getElementById('secondLevelDepartment').value = level2Task.department;
        document.getElementById('secondLevelValueWeight').value = (level2Task.weight * 100).toFixed(1) + '%';
        document.getElementById('secondLevelAmount').value = level2Task.amount;
        document.getElementById('secondLevelCompletionRate').value = level2Task.progress;
        document.getElementById('secondLevelProgressText').textContent = level2Task.progress + '%';
        document.getElementById('secondLevelResponsible').value = level2Task.responsible;
        document.getElementById('secondLevelCompletionStatus').value = level2Task.taskStatus;
        document.getElementById('secondLevelRemark').value = level2Task.secondLevelRemark;
    } else {
        title.textContent = '新增二级流程';
        // 清空表单
        document.querySelectorAll('#secondLevelProcessModal input, #secondLevelProcessModal select, #secondLevelProcessModal textarea').forEach(input => {
            if (input.type === 'range') {
                input.value = '0';
            } else {
                input.value = '';
            }
        });
        document.getElementById('secondLevelProgressText').textContent = '0%';
        document.getElementById('secondLevelCompletionStatus').value = '未完成';
        // 设置默认责任人
        if (taskId && level1TaskId) {
            const task = processTaskSummary.find(t => t.id === taskId);
            if (task) {
                const level1Task = task.level1Tasks.find(t => t.id === level1TaskId);
                if (level1Task) {
                    document.getElementById('secondLevelResponsible').value = level1Task.responsible;
                }
            }
        }
    }

    modal.style.display = 'flex';
    
    // 设置自动计算
    setTimeout(() => {
        setupAutoCalculation();
    }, 100);
}

// 关闭二级流程任务模态框
function closeSecondLevelProcessModal() {
    document.getElementById('secondLevelProcessModal').style.display = 'none';
    editingSecondLevelTaskId = null;
    editingSecondLevelParentTaskId = null;
    editingSecondLevelLevel1TaskId = null;
}

// 更新二级流程进度显示
function updateSecondLevelProgress(value) {
    document.getElementById('secondLevelProgressText').textContent = value + '%';
}

// 保存二级流程任务
// function saveSecondLevelProcess() {
//     const taskName = document.getElementById('secondLevelTaskName').value.trim();
//     const weightStr = document.getElementById('secondLevelValueWeight').value.trim();
//     const amount = parseFloat(document.getElementById('secondLevelAmount').value) || 0;
//     const progress = parseInt(document.getElementById('secondLevelCompletionRate').value) || 0;
//     const responsible = document.getElementById('secondLevelResponsible').value.trim();
//     const taskStatus = document.getElementById('secondLevelCompletionStatus').value;
//     const secondLevelRemark = document.getElementById('secondLevelRemark').value.trim();
    
//     if (!taskName || !responsible) {
//         alert('请填写任务名称和责任人');
//         return;
//     }

//     // 解析权重
//     let weight = 0;
//     if (weightStr.includes('%')) {
//         weight = parseFloat(weightStr.replace('%', '')) / 100;
//     } else {
//         weight = parseFloat(weightStr) || 0;
//     }

//     const task = processTaskSummary.find(t => t.id === editingSecondLevelParentTaskId);
//     if (!task) return;

//     const level1Task = task.level1Tasks.find(t => t.id === editingSecondLevelLevel1TaskId);
//     if (!level1Task) return;

//     // 自动计算金额：一级任务金额 × 权重
//     const calculatedAmount = Math.round(level1Task.amount * weight);

//     const taskData = {
//         taskName,
//         weight,
//         amount: calculatedAmount, // 使用计算出的金额
//         progress,
//         responsible,
//         taskStatus,
//         secondLevelRemark
//     };

//     if (editingSecondLevelTaskId) {
//         // 编辑模式
//         const level2Task = level1Task.level2Tasks.find(t => t.id === editingSecondLevelTaskId);
//         if (level2Task) {
//             Object.assign(level2Task, taskData);
//         }
//     } else {
//         // 新增模式
//         taskData.id = Date.now();
//         level1Task.level2Tasks.push(taskData);
//     }

//     closeSecondLevelProcessModal();
//     // 修复：正确调用渲染函数
//     renderLevel2Tasks(editingSecondLevelParentTaskId, editingSecondLevelLevel1TaskId);
//     // 同时更新上级表格显示
//     renderLevel1Tasks(editingSecondLevelParentTaskId);
//     renderProcessTaskSummaryTable();
// }
// 修改保存二级流程函数
function saveSecondLevelProcess() {
    const taskName = document.getElementById('secondLevelTaskName').value.trim();
    const weightStr = document.getElementById('secondLevelValueWeight').value.trim();
    const progress = parseInt(document.getElementById('secondLevelCompletionRate').value) || 0;
    const responsible = document.getElementById('secondLevelResponsible').value.trim();
    const taskStatus = document.getElementById('secondLevelCompletionStatus').value;
    const secondLevelRemark = document.getElementById('secondLevelRemark').value.trim();
    
    if (!taskName || !responsible) {
        alert('请填写任务名称和责任人');
        return;
    }

    // 解析权重
    let weight = 0;
    if (weightStr.includes('%')) {
        weight = parseFloat(weightStr.replace('%', '')) / 100;
    } else {
        weight = parseFloat(weightStr) || 0;
    }

    const task = processTaskSummary.find(t => t.id === editingSecondLevelParentTaskId);
    if (!task) return;

    const level1Task = task.level1Tasks.find(t => t.id === editingSecondLevelLevel1TaskId);
    if (!level1Task) return;

    const taskData = {
        taskName,
        weight,
        amount: 0, // 将通过calculateTaskAmounts计算
        progress,
        responsible,
        taskStatus,
        secondLevelRemark
    };

    if (editingSecondLevelTaskId) {
        // 编辑模式
        const level2Task = level1Task.level2Tasks.find(t => t.id === editingSecondLevelTaskId);
        if (level2Task) {
            Object.assign(level2Task, taskData);
        }
    } else {
        // 新增模式
        taskData.id = Date.now();
        level1Task.level2Tasks.push(taskData);
    }

    // 重新计算所有金额
    updateTaskAmountsDisplay(editingSecondLevelParentTaskId);
    
    closeSecondLevelProcessModal();
}
// 绑定保存按钮事件
document.getElementById('saveSecondLevelProcessBtn').onclick = saveSecondLevelProcess;

// 新增任务总表任务按钮事件
document.getElementById('addProcessTaskBtn').onclick = function() {
    // 打开新增流程任务模态框
    openProcessTaskModal(false);
};

//操作按钮
//月度，季度，年度操作按钮
document.addEventListener('DOMContentLoaded', (event) => {
    // 获取时间选择器容器
    const timeSelector = document.getElementById('bonusTimeSelector');

    if (timeSelector) {
        // 为容器内的所有按钮添加点击事件监听器
        timeSelector.addEventListener('click', (event) => {
            const target = event.target;

            // 确保点击的是带有 'time-btn' 类的按钮
            if (target.classList.contains('time-btn')) {
                
                // 1. 移除所有兄弟按钮的 'active' 类
                const buttons = timeSelector.querySelectorAll('.time-btn');
                buttons.forEach(button => {
                    button.classList.remove('active');
                });

                // 2. 为当前点击的按钮添加 'active' 类
                target.classList.add('active');

                // 3. （可选）获取当前选中的时间类型，例如 'monthly', 'quarterly', 'yearly'
                const selectedTimeType = target.getAttribute('data-time-type');
                console.log('Selected time period:', selectedTimeType);

                // 在这里可以添加根据选中周期（selectedTimeType）加载数据的逻辑
                // updateBonusStatistics(selectedTimeType);
            }
        });
    }
});

//标准工时配置
let standardTimesRows = [];
let standardTimesIdx = null;
function initStandardTimesRows() {
    standardTimesRows = [];
    const trs = document.querySelectorAll('#standardHoursTable tbody tr');
    trs.forEach(tr => {
        const tds = tr.querySelectorAll('td');
        if (tds.length >= 3) {
            standardTimesRows.push({
                // contractId: tds[0].textContent,
                // orderId: tds[1].textContent,
                taskType: tds[0].textContent,
                standardHours: tds[1].textContent,
                feeRate: tds[2].textContent
            });
        }
    });
}
function openStandardTimesModal(isEdit, idx) {
    document.getElementById('standardHoursModel').style.display = 'flex';
    document.getElementById('standardHoursTitle').innerText = isEdit ? '编辑项目' : '新增项目';
    if (isEdit && idx !== null) {
        const p = standardTimesRows[idx];
        document.getElementById('standardTaskType').value = p.taskType || '';
        document.getElementById('standardHoursTime').value = p.standardHours || '';
        document.getElementById('standardFeeRate').value = p.feeRate || '';
        standardTimesIdx = idx;
    } else {
        document.querySelectorAll('#standardHoursModel input').forEach(i=>i.value='');
        standardTimesIdx = null;
    }
}
function closeStandardHoursModel() {
    document.getElementById('standardHoursModel').style.display = 'none';
}
document.getElementById('addStandardTimesBtn').onclick = function() {
    openStandardTimesModal(false, null);
};
document.getElementById('saveStandardHoursBtn').onclick = function() {
    const p = {
        taskType: document.getElementById('standardTaskType').value,
        standardHours: document.getElementById('standardHoursTime').value,
        feeRate: document.getElementById('standardFeeRate').value
    };
    if (standardTimesIdx !== null) {
        standardTimesRows[standardTimesIdx] = p;
    } else {
        standardTimesRows.push(p);
    }
    closeStandardHoursModel();
    renderStandardTimesTable();
};

window.editStandardTimesRow = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    openStandardTimesModal(true, idx);
};
window.deleteStandardTimesRow = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    if (confirm('确定删除该项目？')) {
        standardTimesRows.splice(idx, 1);
        renderStandardTimesTable();
    }
};
function renderStandardTimesTable() {
    const tbody = document.querySelector('#standardHoursTable tbody');
    tbody.innerHTML = '';
    standardTimesRows.forEach((p, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${p.taskType}</td>
            <td>${p.standardHours}</td>
            <td>${p.feeRate}</td>
            <td class="action-cell">
                <button class="action-btn edit-btn" onclick="editStandardTimesRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteStandardTimesRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}
// ========== 工资档位配置 ==========
let wageUnitRows = [
    {
        level: 8000,
        advanceBonus: 5000
    },
    {
        level: 12000,
        advanceBonus: 4500
    },
    {
        level: 15000,
        advanceBonus: 4000
    }
];
let editingWageUnitIdx = null;

// 渲染工资档位配置表格
function renderWageUnitsTable() {
    const tbody = document.querySelector('#wageUnitsTable tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    wageUnitRows.forEach((row, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.level}</td>
            <td>¥${row.advanceBonus.toLocaleString()}</td>
            <td class="action-cell">
                <button class="action-btn edit-btn" onclick="editWageUnitRow(this)">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="action-btn delete-btn" onclick="deleteWageUnitRow(this)">
                    <i class="fas fa-trash-alt"></i> 删除
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 打开工资档位配置模态框
function openWageUnitModal(isEdit, idx) {
    document.getElementById('wageUnitModal').style.display = 'flex';
    document.getElementById('wageUnitModalTitle').innerText = isEdit ? '编辑工资档位' : '新增工资档位';
    
    if (isEdit && idx !== null) {
        const row = wageUnitRows[idx];
        document.getElementById('wageUnitLevel').value = row.level || '';
        document.getElementById('wageUnitAdvanceBonus').value = row.advanceBonus || '';
        editingWageUnitIdx = idx;
    } else {
        document.getElementById('wageUnitLevel').value = '';
        document.getElementById('wageUnitAdvanceBonus').value = '';
        editingWageUnitIdx = null;
    }
}

// 关闭工资档位配置模态框
function closeWageUnitModal() {
    document.getElementById('wageUnitModal').style.display = 'none';
}

// 新增工资档位按钮
document.getElementById('addWageUnitBtn').onclick = function() {
    openWageUnitModal(false, null);
};

// 保存工资档位
document.getElementById('saveWageUnitBtn').onclick = function() {
    const level = parseFloat(document.getElementById('wageUnitLevel').value) || 0;
    const advanceBonus = parseFloat(document.getElementById('wageUnitAdvanceBonus').value) || 0;
    
    if (level <= 0) {
        alert('请输入有效的工资档位');
        return;
    }
    
    if (advanceBonus <= 0) {
        alert('请输入有效的预支奖金额');
        return;
    }
    
    const row = {
        level: level,
        advanceBonus: advanceBonus
    };
    
    if (editingWageUnitIdx !== null) {
        wageUnitRows[editingWageUnitIdx] = row;
    } else {
        wageUnitRows.push(row);
    }
    
    closeWageUnitModal();
    renderWageUnitsTable();
};

// 编辑工资档位
window.editWageUnitRow = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    openWageUnitModal(true, idx);
};

// 删除工资档位
window.deleteWageUnitRow = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    if (confirm('确定删除该工资档位？')) {
        wageUnitRows.splice(idx, 1);
        renderWageUnitsTable();
    }
};

// 初始化工资档位配置
function initWageUnits() {
    renderWageUnitsTable();
}
// ========== 个人奖金数据管理 ==========
// 个人奖金表格数据 - 按时间类型分组
const personalBonusData = {
    monthly: [
        {
            year: '2025',
            period: '01月',
            confirmedValueHour: 280,
            level: 'B档',
            standardHours: 300,
            advanceBonus: 4500,
            afmCommission: 1000,
            asp: 600,
            adjustedSalary: 300
        },
        {
            year: '2025',
            period: '02月',
            confirmedValueHour: 320,
            level: 'B档',
            standardHours: 300,
            advanceBonus: 5000,
            afmCommission: 1200,
            asp: 800,
            adjustedSalary: 500
        }
    ],
    quarterly: [
        {
            year: '2025',
            period: 'Q1',
            confirmedValueHour: 900,
            level: 'B档',
            standardHours: 900,
            advanceBonus: 13500,
            afmCommission: 3200,
            asp: 2400,
            adjustedSalary: 1300
        },
        {
            year: '2024',
            period: 'Q4',
            confirmedValueHour: 850,
            level: 'B档',
            standardHours: 900,
            advanceBonus: 12750,
            afmCommission: 3000,
            asp: 2200,
            adjustedSalary: 1100
        }
    ],
    yearly: [
        {
            year: '2024',
            period: '全年',
            confirmedValueHour: 3600,
            level: 'B档',
            standardHours: 3600,
            advanceBonus: 54000,
            afmCommission: 12800,
            asp: 9600,
            adjustedSalary: 5200
        },
        {
            year: '2023',
            period: '全年',
            confirmedValueHour: 3400,
            level: 'B档',
            standardHours: 3600,
            advanceBonus: 51000,
            afmCommission: 12000,
            asp: 9000,
            adjustedSalary: 4800
        }
    ]
};

let currentPersonalTimeType = 'monthly';
let personalBonusRows = personalBonusData.monthly;

// 切换个人奖金时间类型
function switchPersonalTimeType(timeType) {
    currentPersonalTimeType = timeType;
    personalBonusRows = personalBonusData[timeType];
    
    // 更新表头
    const periodHeader = document.querySelector('#personal-reward .card:last-child table thead tr th:nth-child(2)');
    if (periodHeader) {
        switch(timeType) {
            case 'monthly':
                periodHeader.textContent = '月份';
                break;
            case 'quarterly':
                periodHeader.textContent = '季度';
                break;
            case 'yearly':
                periodHeader.textContent = '年度';
                break;
        }
    }
    
    renderPersonalBonusTable();
}

// 渲染个人奖金表格（只渲染奖金详情表格）
function renderPersonalBonusTable() {
    const tbody = document.querySelector('#personal-reward .card:last-child table tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    personalBonusRows.forEach((row, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.year}</td>
            <td>${row.period}</td>
            <td>${row.confirmedValueHour}</td>
            <td>${row.level}</td>
            <td>${row.standardHours}</td>
            <td>¥${row.advanceBonus.toLocaleString()}</td>
            <td>¥${row.afmCommission.toLocaleString()}</td>
            <td>¥${row.asp.toLocaleString()}</td>
            <td>¥${row.adjustedSalary.toLocaleString()}</td>
        `;
        tbody.appendChild(tr);
    });
}

// 个人奖金时间选择器事件监听
document.addEventListener('DOMContentLoaded', function() {
    const bonusTimeSelector = document.getElementById('bonusTimeSelector');
    
    if (bonusTimeSelector) {
        bonusTimeSelector.addEventListener('click', function(event) {
            const target = event.target;
            
            if (target.classList.contains('time-btn')) {
                // 移除所有按钮的 active 类
                const buttons = bonusTimeSelector.querySelectorAll('.time-btn');
                buttons.forEach(button => {
                    button.classList.remove('active');
                });
                
                // 为当前点击的按钮添加 active 类
                target.classList.add('active');
                
                // 获取选中的时间类型并切换数据
                const selectedTimeType = target.getAttribute('data-time-type');
                switchPersonalTimeType(selectedTimeType);
            }
        });
    }
    
    // 初始化个人奖金表格
    renderPersonalBonusTable();
}); // ========== 页面初始化 ==========
window.onload = function() {
    initSummaryTasks();
    initProjectRows();
    renderWorkHourTable();
    renderStatSummaryBonusTable();
    renderAllProcessTasks();
    renderFirstLevelProcessTable();
    renderSecondLevelProcessTable();
    initStandardTimesRows();
    renderStandardTimesTable();
    initWageUnits();
    initOutputSummary();
    initRegionalStrategic();
    initAnnualFund();
    initStatSummary(); // 添加统计汇总初始化
};

function viewProjectTasks() {
    document.getElementById('taskSummaryCard').style.display = 'none';
    document.getElementById('projectCard').style.display = 'block';
    document.getElementById('taskCard').style.display = 'none';
    renderProjectTable && renderProjectTable();
}

function showProjectTasks(projectId) {
    document.getElementById('taskSummaryCard').style.display = 'none';
    document.getElementById('projectCard').style.display = 'none';
    document.getElementById('taskCard').style.display = 'block';
    document.getElementById('currentProjectId').textContent = '（项目编号：' + projectId + '）';
    renderTaskTable && renderTaskTable(projectId);
}

function backToSummary() {
    document.getElementById('taskSummaryCard').style.display = 'block';
    document.getElementById('projectCard').style.display = 'none';
    document.getElementById('taskCard').style.display = 'none';
}

function backToProject() {
    document.getElementById('taskSummaryCard').style.display = 'none';
    document.getElementById('projectCard').style.display = 'block';
    document.getElementById('taskCard').style.display = 'none';
}
function closeModal() {
    document.getElementById('taskModal').style.display = 'none';
}
window.editTask = function(btn) {
    const row = btn.closest('tr');
    const tds = row.querySelectorAll('td');
    document.getElementById('taskModal').style.display = 'flex';
    document.getElementById('modalTitle').innerText = '编辑任务';
    document.getElementById('taskId').value = tds[1].textContent;
    document.getElementById('feeRate').value = tds[2].textContent;
    document.getElementById('empId').value = tds[3].textContent;
    document.getElementById('empName').value = tds[4].textContent;
    document.getElementById('department').value = tds[5].textContent;
    document.getElementById('group').value = tds[6].textContent;
    document.getElementById('taskAmount').value = tds[7].textContent.replace('¥','');
    document.getElementById('plannedHours').value = tds[8].textContent;
    document.getElementById('additionalHours').value = tds[9].textContent;
    document.getElementById('extraHours').value = tds[10].textContent;
    document.getElementById('valueHourDisplay').value = tds[11].textContent; // 新增
    document.getElementById('confirmedHours').value = tds[12].textContent;
    document.getElementById('cost').value = tds[13].textContent; // 新增
    document.getElementById('recoverRate').value = tds[14].textContent;
    document.getElementById('hourUtilization').value = tds[15].textContent;
    document.getElementById('rewardFactor').value = tds[16].textContent || '';
    document.getElementById('saveTaskBtn').onclick = function() {
        // 获取当前项目ID
        let projectId = '';
        const projectIdSpan = document.getElementById('currentProjectId');
        if (projectIdSpan) {
            projectId = projectIdSpan.textContent.replace('（项目编号：', '').replace('）', '');
        }
        // 更新 allTasks 中对应任务
        const idx = row.rowIndex - 1;
        const filteredTasks = allTasks.filter(t => t.projectId === projectId);
        const task = filteredTasks[idx];
        if (task) {
            task.taskId = document.getElementById('taskId').value;
            task.feeRate = document.getElementById('feeRate').value;
            task.empId = document.getElementById('empId').value;
            task.empName = document.getElementById('empName').value;
            task.department = document.getElementById('department').value;
            task.group = document.getElementById('group').value;
            task.taskAmount = document.getElementById('taskAmount').value;
            task.plannedHours = document.getElementById('plannedHours').value;
            task.additionalHours = document.getElementById('additionalHours').value;
            task.extraHours = document.getElementById('extraHours').value;
            task.valueHour = document.getElementById('valueHourDisplay').value; // 新增
            task.confirmedHours = document.getElementById('confirmedHours').value;
            task.cost = document.getElementById('cost').value; // 新增
            task.recoverRate = document.getElementById('recoverRate').value; // 新增
            task.hourUtilization = document.getElementById('hourUtilization').value; // 新增
            task.rewardFactor = document.getElementById('rewardFactor').value; // 新增
        }
        closeModal();
        renderTaskTable(projectId);
    };
};

window.deleteTask = function(btn) {
    // 获取当前项目ID
    let projectId = '';
    const projectIdSpan = document.getElementById('currentProjectId');
    if (projectIdSpan) {
        projectId = projectIdSpan.textContent.replace('（项目编号：', '').replace('）', '');
    }
    // 找到当前行在当前项目任务中的索引
    const row = btn.closest('tr');
    const idx = row.rowIndex - 1;
    // 过滤出当前项目的任务
    const filteredTasks = allTasks.filter(t => t.projectId === projectId);
    if (filteredTasks[idx] && confirm('确定要删除该任务吗？')) {
        // 找到该任务在 allTasks 中的真实索引
        const realIdx = allTasks.indexOf(filteredTasks[idx]);
        if (realIdx !== -1) {
            allTasks.splice(realIdx, 1);
            renderTaskTable(projectId);
        }
    }
};

// ========== 产值汇总统计 ==========
let outputSummaryRows = [
    {
        period: '2025-Q1',
        assignedOutput: '120000',
        confirmedOutput: '110000',
        remainingAmount: '10000',
        closedOutput: '100000',
        bdAmount: '30000',
        bdRemaining: '5000',
        strategicAmount: '20000',
        strategicRemaining: '8000'
    },
    {
        period: '2025-Q2',
        assignedOutput: '130000',
        confirmedOutput: '120000',
        remainingAmount: '10000',
        closedOutput: '110000',
        bdAmount: '35000',
        bdRemaining: '6000',
        strategicAmount: '25000',
        strategicRemaining: '9000'
    }
];
let editingOutputIdx = null;

// 渲染产值汇总统计表格
function renderOutputSummaryTable() {
    const tbody = document.querySelector('#outputSummaryTable tbody');
    tbody.innerHTML = '';
    outputSummaryRows.forEach((row, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.period}</td>
            <td>¥${row.assignedOutput}</td>
            <td>¥${row.confirmedOutput}</td>
            <td>¥${row.remainingAmount}</td>
            <td>¥${row.closedOutput}</td>
            <td>¥${row.bdAmount}</td>
            <td>¥${row.bdRemaining}</td>
            <td>¥${row.strategicAmount}</td>
            <td>¥${row.strategicRemaining}</td>
            <td>
                <button class="action-btn edit-btn" onclick="editOutputSummaryRow(${idx})"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteOutputSummaryRow(${idx})"><i class="fas fa-trash-alt"></i> 删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 打开产值汇总统计模态框
function openOutputSummaryModal(isEdit, idx) {
    document.getElementById('outputSummaryModal').style.display = 'flex';
    document.getElementById('outputSummaryModalTitle').innerText = isEdit ? '编辑产值汇总' : '新增产值汇总';
    if (isEdit && idx !== null) {
        const row = outputSummaryRows[idx];
        document.getElementById('outputPeriod').value = row.period || '';
        document.getElementById('outputAssigned').value = row.assignedOutput || '';
        document.getElementById('outputConfirmed').value = row.confirmedOutput || '';
        document.getElementById('outputRemaining').value = row.remainingAmount || '';
        document.getElementById('outputClosed').value = row.closedOutput || '';
        document.getElementById('outputBdAmount').value = row.bdAmount || '';
        document.getElementById('outputBdRemaining').value = row.bdRemaining || '';
        document.getElementById('outputStrategicAmount').value = row.strategicAmount || '';
        document.getElementById('outputStrategicRemaining').value = row.strategicRemaining || '';
        editingOutputIdx = idx;
    } else {
        document.querySelectorAll('#outputSummaryModal input').forEach(i=>i.value='');
        editingOutputIdx = null;
    }
}

// 关闭产值汇总统计模态框
function closeOutputSummaryModal() {
    document.getElementById('outputSummaryModal').style.display = 'none';
}

// 新增产值汇总统计按钮
document.getElementById('addOutputSummaryBtn').onclick = function() {
    openOutputSummaryModal(false, null);
};

// 保存产值汇总统计按钮
document.getElementById('saveOutputSummaryBtn').onclick = function() {
    const row = {
        period: document.getElementById('outputPeriod').value,
        assignedOutput: document.getElementById('outputAssigned').value,
        confirmedOutput: document.getElementById('outputConfirmed').value,
        remainingAmount: document.getElementById('outputRemaining').value,
        closedOutput: document.getElementById('outputClosed').value,
        bdAmount: document.getElementById('outputBdAmount').value,
        bdRemaining: document.getElementById('outputBdRemaining').value,
        strategicAmount: document.getElementById('outputStrategicAmount').value,
        strategicRemaining: document.getElementById('outputStrategicRemaining').value
    };
    
    if (editingOutputIdx !== null) {
        outputSummaryRows[editingOutputIdx] = row;
    } else {
        outputSummaryRows.push(row);
    }
    closeOutputSummaryModal();
    renderOutputSummaryTable();
};

// 编辑产值汇总统计行
window.editOutputSummaryRow = function(idx) {
    openOutputSummaryModal(true, idx);
};

// 删除产值汇总统计行
window.deleteOutputSummaryRow = function(idx) {
    if (confirm('确定删除该产值汇总记录？')) {
        outputSummaryRows.splice(idx, 1);
        renderOutputSummaryTable();
    }
};

// 初始化函数
function initOutputSummary() {
    renderOutputSummaryTable();
}
// 在页面加载完成后初始化所有表格
document.addEventListener('DOMContentLoaded', function() {
    // ... 其他初始化代码 ...
    initWageUnits();
    // ... 其他初始化代码 ...
});

// ========== 地区战略分配 ==========
let regionalStrategicRows = [
    {
        year: '2025',
        region: '华北地区',
        bdRatio: '15%',
        bdAmount: 150000,
        strategicRatio: '8%',
        strategicAmount: 80000
    },
    {
        year: '2025',
        region: '华东地区',
        bdRatio: '18%',
        bdAmount: 200000,
        strategicRatio: '10%',
        strategicAmount: 110000
    },
    {
        year: '2025',
        region: '华南地区',
        bdRatio: '12%',
        bdAmount: 120000,
        strategicRatio: '6%',
        strategicAmount: 60000
    },
    {
        year: '2024',
        region: '西部地区',
        bdRatio: '10%',
        bdAmount: 100000,
        strategicRatio: '5%',
        strategicAmount: 50000
    },
    {
        year: '2024',
        region: '东北地区',
        bdRatio: '8%',
        bdAmount: 80000,
        strategicRatio: '4%',
        strategicAmount: 40000
    }
];

let filteredRegionalRows = [...regionalStrategicRows];
let editingRegionalIdx = null;

// 渲染地区战略分配表格
function renderRegionalStrategicTable() {
    const tbody = document.querySelector('#regionalStrategicTable tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    filteredRegionalRows.forEach((row, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.year}</td>
            <td>${row.region}</td>
            <td>${row.bdRatio}</td>
            <td>¥${row.bdAmount.toLocaleString()}</td>
            <td>${row.strategicRatio}</td>
            <td>¥${row.strategicAmount.toLocaleString()}</td>
            <td>
                <button class="action-btn edit-btn" onclick="editRegionalRow(${idx})"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteRegionalRow(${idx})"><i class="fas fa-trash-alt"></i> 删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 搜索筛选功能
function searchRegionalData() {
    const regionFilter = document.getElementById('regionFilter').value;
    const yearFilter = document.getElementById('yearFilter').value;
    
    filteredRegionalRows = regionalStrategicRows.filter(row => {
        const regionMatch = !regionFilter || row.region === regionFilter;
        const yearMatch = !yearFilter || row.year === yearFilter;
        return regionMatch && yearMatch;
    });
    
    renderRegionalStrategicTable();
}

// 打开地区战略分配模态框
function openRegionalStrategicModal(isEdit, idx) {
    document.getElementById('regionalStrategicModal').style.display = 'flex';
    document.getElementById('regionalStrategicModalTitle').innerText = isEdit ? '编辑地区战略分配' : '新增地区战略分配';
    
    if (isEdit && idx !== null) {
        const row = filteredRegionalRows[idx];
        document.getElementById('regionalYear').value = row.year || '';
        document.getElementById('regionalRegion').value = row.region || '';
        document.getElementById('regionalBdRatio').value = row.bdRatio || '';
        document.getElementById('regionalBdAmount').value = row.bdAmount || '';
        document.getElementById('regionalStrategicRatio').value = row.strategicRatio || '';
        document.getElementById('regionalStrategicAmount').value = row.strategicAmount || '';
        editingRegionalIdx = idx;
    } else {
        document.getElementById('regionalYear').value = '2025';
        document.getElementById('regionalRegion').value = '华北地区';
        document.getElementById('regionalBdRatio').value = '';
        document.getElementById('regionalBdAmount').value = '';
        document.getElementById('regionalStrategicRatio').value = '';
        document.getElementById('regionalStrategicAmount').value = '';
        editingRegionalIdx = null;
    }
}

// 关闭地区战略分配模态框
function closeRegionalStrategicModal() {
    document.getElementById('regionalStrategicModal').style.display = 'none';
}

// 保存地区战略分配
function saveRegionalStrategic() {
    const row = {
        year: document.getElementById('regionalYear').value,
        region: document.getElementById('regionalRegion').value,
        bdRatio: document.getElementById('regionalBdRatio').value,
        bdAmount: parseFloat(document.getElementById('regionalBdAmount').value) || 0,
        strategicRatio: document.getElementById('regionalStrategicRatio').value,
        strategicAmount: parseFloat(document.getElementById('regionalStrategicAmount').value) || 0
    };
    
    if (editingRegionalIdx !== null) {
        // 找到原始数据中的索引
        const originalIdx = regionalStrategicRows.findIndex(item => 
            item === filteredRegionalRows[editingRegionalIdx]
        );
        if (originalIdx !== -1) {
            regionalStrategicRows[originalIdx] = row;
        }
    } else {
        regionalStrategicRows.push(row);
    }
    
    closeRegionalStrategicModal();
    searchRegionalData(); // 重新筛选和渲染
}

// 编辑地区战略分配
window.editRegionalRow = function(idx) {
    openRegionalStrategicModal(true, idx);
};

// 删除地区战略分配
window.deleteRegionalRow = function(idx) {
    if (confirm('确定删除该地区战略分配记录？')) {
        const originalIdx = regionalStrategicRows.findIndex(row => 
            row === filteredRegionalRows[idx]
        );
        if (originalIdx !== -1) {
            regionalStrategicRows.splice(originalIdx, 1);
            searchRegionalData(); // 重新筛选和渲染
        }
    }
};

// 初始化地区战略分配
function initRegionalStrategic() {
    filteredRegionalRows = [...regionalStrategicRows]; // 确保初始显示所有数据
    renderRegionalStrategicTable();
}

// 搜索按钮事件
document.addEventListener('DOMContentLoaded', function() {
    const searchBtn = document.getElementById('searchRegionalBtn');
    if (searchBtn) {
        searchBtn.addEventListener('click', searchRegionalData);
    }
    
    const addRegionalBtn = document.getElementById('addRegionalBtn');
    if (addRegionalBtn) {
        addRegionalBtn.addEventListener('click', function() {
            openRegionalStrategicModal(false, null);
        });
    }
});  // ========== 年度资金分配 ==========
let annualFundRows = [
    {
        year: '2025',
        businessUnit: '西部事业部',
        annualBdAmount: 500000,
        strategicAmount: 200000
    },
    {
        year: '2025',
        businessUnit: '华南事业群',
        annualBdAmount: 800000,
        strategicAmount: 350000
    },
    {
        year: '2025',
        businessUnit: '华东事业群',
        annualBdAmount: 1200000,
        strategicAmount: 500000
    },
    {
        year: '2024',
        businessUnit: '北方事业群',
        annualBdAmount: 600000,
        strategicAmount: 250000
    },
    {
        year: '2024',
        businessUnit: '西部事业部',
        annualBdAmount: 450000,
        strategicAmount: 180000
    }
];

let filteredAnnualRows = [...annualFundRows];
let editingAnnualIdx = null;

// 渲染年度资金分配表格
function renderAnnualFundTable() {
    const tbody = document.querySelector('#annualFundTable tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    filteredAnnualRows.forEach((row, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.year}</td>
            <td>${row.businessUnit}</td>
            <td>¥${row.annualBdAmount.toLocaleString()}</td>
            <td>¥${row.strategicAmount.toLocaleString()}</td>
            <td>
                <button class="action-btn edit-btn" onclick="editAnnualFundRow(${idx})"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteAnnualFundRow(${idx})"><i class="fas fa-trash-alt"></i> 删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 搜索筛选功能
function searchAnnualFundData() {
    const yearFilter = document.getElementById('annualYearFilter').value;
    
    filteredAnnualRows = annualFundRows.filter(row => {
        const yearMatch = !yearFilter || row.year === yearFilter;
        return yearMatch;
    });
    
    renderAnnualFundTable();
}

// 打开年度资金分配模态框
function openAnnualFundModal(isEdit, idx) {
    document.getElementById('annualFundModal').style.display = 'flex';
    document.getElementById('annualFundModalTitle').innerText = isEdit ? '编辑年度资金分配' : '新增年度资金分配';
    
    if (isEdit && idx !== null) {
        const row = filteredAnnualRows[idx];
        document.getElementById('annualFundYear').value = row.year || '';
        document.getElementById('annualFundBusinessUnit').value = row.businessUnit || '';
        document.getElementById('annualFundBdAmount').value = row.annualBdAmount || '';
        document.getElementById('annualFundStrategicAmount').value = row.strategicAmount || '';
        editingAnnualIdx = idx;
    } else {
        document.getElementById('annualFundYear').value = '2025';
        document.getElementById('annualFundBusinessUnit').value = '';
        document.getElementById('annualFundBdAmount').value = '';
        document.getElementById('annualFundStrategicAmount').value = '';
        editingAnnualIdx = null;
    }
}

// 关闭年度资金分配模态框
function closeAnnualFundModal() {
    document.getElementById('annualFundModal').style.display = 'none';
}

// 保存年度资金分配
function saveAnnualFund() {
    const row = {
        year: document.getElementById('annualFundYear').value,
        businessUnit: document.getElementById('annualFundBusinessUnit').value,
        annualBdAmount: parseFloat(document.getElementById('annualFundBdAmount').value) || 0,
        strategicAmount: parseFloat(document.getElementById('annualFundStrategicAmount').value) || 0
    };
    
    if (editingAnnualIdx !== null) {
        // 找到原始数据中的索引
        const originalIdx = annualFundRows.findIndex(item => 
            item === filteredAnnualRows[editingAnnualIdx]
        );
        if (originalIdx !== -1) {
            annualFundRows[originalIdx] = row;
        }
    } else {
        annualFundRows.push(row);
    }
    
    closeAnnualFundModal();
    searchAnnualFundData(); // 重新筛选和渲染
}

// 编辑年度资金分配
window.editAnnualFundRow = function(idx) {
    openAnnualFundModal(true, idx);
};

// 删除年度资金分配
window.deleteAnnualFundRow = function(idx) {
    if (confirm('确定删除该年度资金分配记录？')) {
        const originalIdx = annualFundRows.findIndex(row => 
            row === filteredAnnualRows[idx]
        );
        if (originalIdx !== -1) {
            annualFundRows.splice(originalIdx, 1);
            searchAnnualFundData(); // 重新筛选和渲染
        }
    }
};

// 初始化年度资金分配
function initAnnualFund() {
    filteredAnnualRows = [...annualFundRows]; // 确保初始显示所有数据
    renderAnnualFundTable();
}

// 在 DOMContentLoaded 事件监听器中添加年度资金分配的事件绑定
document.addEventListener('DOMContentLoaded', function() {
    // ... 其他事件绑定 ...
    
    // 年度资金分配搜索按钮事件
    const searchAnnualBtn = document.getElementById('searchAnnualBtn');
    if (searchAnnualBtn) {
        searchAnnualBtn.addEventListener('click', searchAnnualFundData);
    }
    
    const addAnnualFundBtn = document.getElementById('addAnnualFundBtn');
    if (addAnnualFundBtn) {
        addAnnualFundBtn.addEventListener('click', function() {
            openAnnualFundModal(false, null);
        });
    }
    
    // 保存年度资金分配按钮事件
    const saveAnnualFundBtn = document.getElementById('saveAnnualFundBtn');
    if (saveAnnualFundBtn) {
        saveAnnualFundBtn.addEventListener('click', saveAnnualFund);
    }
});  // ========== 页面切换 ==========
document.querySelectorAll('.sidebar-menu .menu-item').forEach(item => {
    item.addEventListener('click', function() {
        document.querySelectorAll('.sidebar-menu .menu-item').forEach(i => i.classList.remove('active'));
        this.classList.add('active');
        Object.values(menuPageMap).forEach(id => {
            const page = document.getElementById(id);
            if (page) page.style.display = 'none';
        });
        const pageId = this.getAttribute('data-page');
        if (pageId) {
            const page = document.getElementById(pageId);
            if (page) {
                page.style.display = 'block';
                if (pageId === 'value-hour') {
                    document.getElementById('taskSummaryCard').style.display = 'block';
                    document.getElementById('projectCard').style.display = 'none';
                    document.getElementById('taskCard').style.display = 'none';
                    renderSummaryTable();
                } else if (pageId === 'annual-fund') {
                    initAnnualFund(); // 初始化年度资金分配数据
                } else if (pageId === 'regional-strategic') {
                    initRegionalStrategic(); // 初始化地区战略分配数据
                } else if (pageId === 'statistical-summary') {
                    initStatSummary(); // 初始化统计汇总数据
                }
            }
        }
    });
});  // ========== 页面初始化 ==========

function viewProjectTasks() {
    document.getElementById('taskSummaryCard').style.display = 'none';
    document.getElementById('projectCard').style.display = 'block';
};

// 保存个人任务的事件监听器
document.addEventListener('DOMContentLoaded', function() {
    const savePersonalTaskBtn = document.getElementById('savePersonalTaskBtn');
    if (savePersonalTaskBtn) {
        savePersonalTaskBtn.onclick = function() {
            const name = document.getElementById('personalTaskName').value.trim();
            const status = document.getElementById('personalTaskStatus').value;

            if (!name) {
                alert('请输入流程名称');
                return;
            }

            if (editingPersonalTaskId) {
                // 编辑模式
                const task = personalTasks[currentPersonalTaskType].find(t => t.id === editingPersonalTaskId);
                if (task) {
                    task.name = name;
                    task.status = status;
                }
            } else {
                // 新增模式
                const newId = Math.max(...Object.values(personalTasks).flat().map(t => t.id), 0) + 1;
                personalTasks[currentPersonalTaskType].push({
                    id: newId,
                    name: name,
                    status: status
                });
            }

            renderPersonalTaskTable(currentPersonalTaskType);
            closePersonalTaskModal();
        };
    }

    // 初始化个人任务表格
    initPersonalTasks();
});

// ========== 我的价值工时 ==========
let valueHourRows = [
    {
        taskId: 'AACN27212',
        taskDesc: '运营服务、专业飞行、市场开发',
        taskAmount: 7000,
        plannedHours: 18,
        reportedHours: 18,
        unitValueHours: 14,
        confirmedValueHours: 15,
        taskStatus: '未封闭',
        transportFee: 0.00,
        accommodationFee: 0.00,
        otherFee: 0.00,
        efficiencyRatio: '93.45%',
        subsidyCoeff: 1,
        contractAttr: '南京分中心',
        responsiblePerson: '王勇',
        remark: '7000'
    },
    {
        taskId: 'AACN27223',
        taskDesc: '运营服务、专业飞行、市场开发',
        taskAmount: 12500,
        plannedHours: 30,
        reportedHours: 33,
        unitValueHours: 25,
        confirmedValueHours: 25,
        taskStatus: '未封闭',
        transportFee: 1020.00,
        accommodationFee: 350.00,
        otherFee: 200.00,
        efficiencyRatio: '87.44%',
        subsidyCoeff: 1,
        contractAttr: '南京分中心',
        responsiblePerson: '王勇',
        remark: '12500'
    },
    {
        taskId: 'ABCN66532',
        taskDesc: '运营服务、专业飞行、市场开发',
        taskAmount: 16000,
        plannedHours: 35,
        reportedHours: 40,
        unitValueHours: 32,
        confirmedValueHours: 32,
        taskStatus: '已封闭',
        transportFee: 834.00,
        accommodationFee: 700.00,
        otherFee: 400.00,
        efficiencyRatio: '87.91%',
        subsidyCoeff: 1,
        contractAttr: '南京分中心',
        responsiblePerson: '王教授',
        remark: '16000'
    },
    {
        taskId: 'BBN2453',
        taskDesc: '运营服务、专业飞行、市场开发',
        taskAmount: 11200,
        plannedHours: 25,
        reportedHours: 25,
        unitValueHours: 22.4,
        confirmedValueHours: 22,
        taskStatus: '未封闭',
        transportFee: 220,
        accommodationFee: 300,
        otherFee: 185,
        efficiencyRatio: '93.71%',
        subsidyCoeff: 1.4,
        contractAttr: '南京分中心',
        responsiblePerson: '郑师兄',
        remark: '8000'
    },
    {
        taskId: 'AACN27234',
        taskDesc: '运营服务、专业飞行、市场开发',
        taskAmount: 14400,
        plannedHours: 35,
        reportedHours: 20,
        unitValueHours: 28.8,
        confirmedValueHours: 12,
        taskStatus: '未封闭',
        transportFee: 660,
        accommodationFee: 350,
        otherFee: 180,
        efficiencyRatio: '45.42%',
        subsidyCoeff: 1,
        contractAttr: '南京分中心',
        responsiblePerson: '王勇',
        remark: '14400'
    },
    {
        taskId: 'AACN27245',
        taskDesc: '运营服务、专业飞行、市场开发',
        taskAmount: 18760,
        plannedHours: 40,
        reportedHours: 28,
        unitValueHours: 37.52,
        confirmedValueHours: 10,
        taskStatus: '未封闭',
        transportFee: 100,
        accommodationFee: 245,
        otherFee: 180,
        efficiencyRatio: '27.42%',
        subsidyCoeff: 1,
        contractAttr: '南京分中心',
        responsiblePerson: '王勇',
        remark: '18760'
    }
];

let editingValueHourIdx = null;

// 渲染价值工时表格
function renderValueHoursTable() {
    const tbody = document.querySelector('#valueHoursTable tbody');
    if (!tbody) return;

    tbody.innerHTML = '';
    valueHourRows.forEach((row, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.taskId}</td>
            <td>${row.taskDesc}</td>
            <td>${row.taskAmount}</td>
            <td>${row.plannedHours}</td>
            <td>${row.reportedHours}</td>
            <td>${row.unitValueHours}</td>
            <td>${row.confirmedValueHours}</td>
            <td>${row.taskStatus}</td>
            <td>${row.transportFee.toFixed(2)}</td>
            <td>${row.accommodationFee.toFixed(2)}</td>
            <td>${row.otherFee.toFixed(2)}</td>
            <td>${row.efficiencyRatio}</td>
            <td>${row.subsidyCoeff}</td>
            <td>${row.contractAttr}</td>
            <td>${row.responsiblePerson}</td>
            <td>${row.remark}</td>
            <td class="action-cell">
                <button class="action-btn edit-btn" onclick="editValueHourRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                <button class="action-btn delete-btn" onclick="deleteValueHourRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 打开价值工时模态框
function openValueHourModal(isEdit, idx) {
    document.getElementById('valueHourModal').style.display = 'flex';
    document.getElementById('valueHourModalTitle').innerText = isEdit ? '编辑价值工时' : '新增价值工时';

    if (isEdit && idx !== null) {
        const row = valueHourRows[idx];
        document.getElementById('vhTaskId').value = row.taskId || '';
        document.getElementById('vhTaskDesc').value = row.taskDesc || '';
        document.getElementById('vhTaskAmount').value = row.taskAmount || '';
        document.getElementById('vhPlannedHours').value = row.plannedHours || '';
        document.getElementById('vhReportedHours').value = row.reportedHours || '';
        document.getElementById('vhUnitValueHours').value = row.unitValueHours || '';
        document.getElementById('vhConfirmedValueHours').value = row.confirmedValueHours || '';
        document.getElementById('vhTaskStatus').value = row.taskStatus || '';
        document.getElementById('vhTransportFee').value = row.transportFee || '';
        document.getElementById('vhAccommodationFee').value = row.accommodationFee || '';
        document.getElementById('vhOtherFee').value = row.otherFee || '';
        document.getElementById('vhEfficiencyRatio').value = row.efficiencyRatio || '';
        document.getElementById('vhSubsidyCoeff').value = row.subsidyCoeff || '';
        document.getElementById('vhContractAttr').value = row.contractAttr || '';
        document.getElementById('vhResponsiblePerson').value = row.responsiblePerson || '';
        document.getElementById('vhRemark').value = row.remark || '';
        editingValueHourIdx = idx;
    } else {
        // 清空表单
        document.getElementById('vhTaskId').value = '';
        document.getElementById('vhTaskDesc').value = '';
        document.getElementById('vhTaskAmount').value = '';
        document.getElementById('vhPlannedHours').value = '';
        document.getElementById('vhReportedHours').value = '';
        document.getElementById('vhUnitValueHours').value = '';
        document.getElementById('vhConfirmedValueHours').value = '';
        document.getElementById('vhTaskStatus').value = '未封闭';
        document.getElementById('vhTransportFee').value = '';
        document.getElementById('vhAccommodationFee').value = '';
        document.getElementById('vhOtherFee').value = '';
        document.getElementById('vhEfficiencyRatio').value = '';
        document.getElementById('vhSubsidyCoeff').value = '1';
        document.getElementById('vhContractAttr').value = '';
        document.getElementById('vhResponsiblePerson').value = '';
        document.getElementById('vhRemark').value = '';
        editingValueHourIdx = null;
    }
}

// 关闭价值工时模态框
function closeValueHourModal() {
    document.getElementById('valueHourModal').style.display = 'none';
}

// 计算工效比
function calculateEfficiencyRatio() {
    const plannedHours = parseFloat(document.getElementById('vhPlannedHours').value) || 0;
    const reportedHours = parseFloat(document.getElementById('vhReportedHours').value) || 0;

    if (reportedHours > 0) {
        const ratio = (plannedHours / reportedHours * 100).toFixed(2) + '%';
        document.getElementById('vhEfficiencyRatio').value = ratio;
    }
}

// 新增价值工时按钮
document.getElementById('addValueHourBtn').onclick = function() {
    openValueHourModal(false, null);
};

// 保存价值工时
document.getElementById('saveValueHourBtn').onclick = function() {
    const taskId = document.getElementById('vhTaskId').value;
    const taskDesc = document.getElementById('vhTaskDesc').value;
    const taskAmount = parseFloat(document.getElementById('vhTaskAmount').value) || 0;
    const plannedHours = parseFloat(document.getElementById('vhPlannedHours').value) || 0;
    const reportedHours = parseFloat(document.getElementById('vhReportedHours').value) || 0;
    const unitValueHours = parseFloat(document.getElementById('vhUnitValueHours').value) || 0;
    const confirmedValueHours = parseFloat(document.getElementById('vhConfirmedValueHours').value) || 0;
    const taskStatus = document.getElementById('vhTaskStatus').value;
    const transportFee = parseFloat(document.getElementById('vhTransportFee').value) || 0;
    const accommodationFee = parseFloat(document.getElementById('vhAccommodationFee').value) || 0;
    const otherFee = parseFloat(document.getElementById('vhOtherFee').value) || 0;
    const subsidyCoeff = parseFloat(document.getElementById('vhSubsidyCoeff').value) || 1;
    const contractAttr = document.getElementById('vhContractAttr').value;
    const responsiblePerson = document.getElementById('vhResponsiblePerson').value;
    const remark = document.getElementById('vhRemark').value;

    if (!taskId) {
        alert('请输入任务编号');
        return;
    }

    if (!taskDesc) {
        alert('请输入任务描述');
        return;
    }

    // 计算工效比
    const efficiencyRatio = reportedHours > 0 ? (plannedHours / reportedHours * 100).toFixed(2) + '%' : '0%';

    const row = {
        taskId: taskId,
        taskDesc: taskDesc,
        taskAmount: taskAmount,
        plannedHours: plannedHours,
        reportedHours: reportedHours,
        unitValueHours: unitValueHours,
        confirmedValueHours: confirmedValueHours,
        taskStatus: taskStatus,
        transportFee: transportFee,
        accommodationFee: accommodationFee,
        otherFee: otherFee,
        efficiencyRatio: efficiencyRatio,
        subsidyCoeff: subsidyCoeff,
        contractAttr: contractAttr,
        responsiblePerson: responsiblePerson,
        remark: remark
    };

    if (editingValueHourIdx !== null) {
        valueHourRows[editingValueHourIdx] = row;
    } else {
        valueHourRows.push(row);
    }

    closeValueHourModal();
    renderValueHoursTable();
};

// 编辑价值工时行
window.editValueHourRow = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    openValueHourModal(true, idx);
};

// 删除价值工时行
window.deleteValueHourRow = function(btn) {
    const idx = btn.closest('tr').rowIndex - 1;
    if (confirm('确定删除该价值工时记录？')) {
        valueHourRows.splice(idx, 1);
        renderValueHoursTable();
    }
};

// 添加工时变化监听器来自动计算工效比
document.addEventListener('DOMContentLoaded', function() {
    const plannedHoursInput = document.getElementById('vhPlannedHours');
    const reportedHoursInput = document.getElementById('vhReportedHours');

    if (plannedHoursInput && reportedHoursInput) {
        plannedHoursInput.addEventListener('input', calculateEfficiencyRatio);
        reportedHoursInput.addEventListener('input', calculateEfficiencyRatio);
    }
});

// 初始化价值工时表格
function initValueHours() {
    renderValueHoursTable();
}

// 在页面加载完成后初始化价值工时表格
document.addEventListener('DOMContentLoaded', function() {
    initValueHours();
});
