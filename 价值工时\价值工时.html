<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价值工时体系台账管理系统</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="./CSS/主页.css">
    <link rel="stylesheet" href="./libs/fontawesome.css">
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-container">
        <div class="login-right">
            <div class="login-header">
                <h2>价值工时体系台账管理系统</h2>
                <p>请输入您的登录信息</p>
            </div>
            
            <form class="login-form">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" class="form-control" placeholder="请输入用户名" required>
                </div>

                <div class="form-group">
                    <label for="employeeId">工号</label>
                    <input type="text" id="employeeId" class="form-control" placeholder="请输入工号" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" class="form-control" placeholder="请输入密码" required>
                </div>
                
                <div class="remember-forgot">
                    <div class="remember">
                        <input type="checkbox" id="remember">
                        <label for="remember">记住我</label>
                    </div>
                    <a href="#" class="forgot-password">忘记密码?</a>
                </div>
                
                <button type="button" id="loginBtn" class="btn">登录系统</button>
            </form>
            
            <div class="language-selector">
                <span>语言:</span>
                <a href="#">中文</a>
                <a href="#">English</a>
            </div>
            
            <div class="login-footer">
                <p>&copy; 2025 北京思诺德恩管理咨询有限公司. 版权所有.</p>
            </div>
        </div>
        <!-- 新增的右下角文字 -->
        <div class="bottom-right-text">
            <div class="sin-occident">SinOccident</div>
            <div class="consulting">Consulting</div>
        </div>
    </div>
    
    <!-- 主应用界面 -->
    <div id="appPage" class="app-container">
        <header class="app-header">
            <button class="menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                <span>价值工时体系</span>
            </div>
            <div class="app-title">启动数字化管理---建立价值工时体系，搭建公司经营绩效台账</div>
            <div class="user-menu">
                <div class="user-info">
                    <div class="user-avatar">张</div>
                    <div class="user-name">张经理</div>
                </div>
                <button class="action-btn">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="action-btn">
                    <i class="fas fa-bell"></i>
                </button>
            </div>
        </header>
        
        <div class="app-main">
            <aside class="sidebar">
                <ul class="sidebar-menu">
                    <li class="menu-title">绩效台账</li>
                    <li class="menu-item active" data-page="personal-page">
                        <i class="fas fa-home"></i>
                        <span>我的首页</span>
                    </li>
                    <li class="menu-item" data-page="procedure-assignment">
                        <i class="fas fa-tasks"></i>
                        <span>流程任务</span>
                    </li>
                    <li class="menu-item" data-page="value-hour">
                        <i class="fas fa-chart-bar"></i>
                        <span>价值工时</span>
                    </li>

                    <li class="menu-item" data-page="value-hour-add">
                        <i class="fas fa-chart-pie"></i>
                        <span>价值工时汇总</span>
                    </li>
                    <li class="menu-item" data-page="output-add">
                        <i class="fas fa-chart-line"></i>
                        <span>产值汇总统计</span>
                    </li>

                    <li class="menu-title">奖金管理</li>
                    <li class="menu-item" data-page="personal-reward">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>个人奖金</span>
                    </li>
                    <li class="menu-item" data-page="my-value-hours">
                        <i class="fas fa-clock"></i>
                        <span>我的价值工时</span>
                    </li>
                    <li class="menu-item" data-page="statistical-summary">
                        <i class="fas fa-chart-area"></i>
                        <span>统计汇总</span>
                    </li>
                    <li class="menu-item" data-page="statitic-hour">
                        <i class="fas fa-cog"></i>
                        <span>标准工时配置</span>
                    </li>
                    <li class="menu-item" data-page="wage-units">
                        <i class="fas fa-sliders-h"></i>
                        <span>工资单位配置</span>
                    </li>
                    <li class="menu-item" data-page="annual-fund">
                        <i class="fas fa-money-check-alt"></i>
                        <span>年度资金分配</span>
                    </li>
                    <li class="menu-item" data-page="regional-strategic">
                        <i class="fas fa-map-marked-alt"></i>
                        <span>地区战略分配</span>
                    </li>
                </ul>
                    <!-- 新增的左下角文字 -->
                <div class="bottom-left-text">
                    <div class="sin-occident">SinOccident</div>
                    <div class="consulting">Consulting</div>
                </div>
            </aside>
            
            <main class="content">
                <!-- 我的首页 -->
                <div id="personal-page" style="display:block;">
                    <div class="page-header">
                        <h1 class="page-title">个人首页</h1>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-title">
                                <i class="fas fa-clock"></i> 本月总工时
                            </div>
                            <div class="stat-value">128.5</div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 12% 高于上月
                            </div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-title">
                                <i class="fas fa-check-circle"></i> 完成任务数
                            </div>
                            <div class="stat-value">24</div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i> 8% 高于上月
                            </div>
                        </div>
                        <!-- <div class="stat-card warning">
                            <div class="stat-title">
                                <i class="fas fa-money-bill-wave"></i> 预计奖金
                            </div>
                            <div class="stat-value">¥8,650</div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i> 5% 低于上月
                            </div>
                        </div> -->
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-calendar-alt"></i> 工时分布日历
                        </div>
                        <div class="calendar-chart" id="workHourCalendar">
                            <!-- 日历将通过JavaScript生成 -->
                        </div>
                    </div>

                    <!-- 个人首页四个表格 -->
                    <div class="personal-tables-container">
                        <!-- 我的待办事项列表 -->
                        <div class="card personal-table-card">
                            <div class="card-header">
                                <h3 class="card-title">我的待办事项列表</h3>
                                <div>
                                    <button class="btn btn-success" onclick="openPersonalTaskModal('todo')">新增待办</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <table id="todoTaskTable" class="personal-table">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>审核状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 动态渲染内容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 我的已办事项列表 -->
                        <div class="card personal-table-card">
                            <div class="card-header">
                                <h3 class="card-title">我的已办事项列表</h3>
                                <div>
                                    <button class="btn btn-success" onclick="openPersonalTaskModal('done')">新增已办</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <table id="doneTaskTable" class="personal-table">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>审核状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 动态渲染内容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 我发起的流程列表 -->
                        <div class="card personal-table-card">
                            <div class="card-header">
                                <h3 class="card-title">我发起的流程列表</h3>
                                <div>
                                    <button class="btn btn-success" onclick="openPersonalTaskModal('done')">新增流程</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <table id="initiatedTaskTable" class="personal-table">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>审核状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 动态渲染内容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 已办结的流程列表 -->
                        <div class="card personal-table-card">
                            <div class="card-header">
                                <h3 class="card-title">已办结的流程列表</h3>
                                <div>
                                    <button class="btn btn-success" onclick="openPersonalTaskModal('done')">新增流程</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <table id="completedTaskTable" class="personal-table">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>审核状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 动态渲染内容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 流程任务 -->
                <div id="procedure-assignment" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">流程任务</h1>
                    </div>
                    <!-- 任务总表 -->
                    <div class="card" id="processTaskSummaryCard">
                        <div class="card-header">
                            <h3 class="card-title">任务总表</h3>
                            <div class="card-actions" style="display: flex; gap: 8px;">
                                <button id="addProcessTaskBtn" class="btn btn-success" onclick="openProcessTaskModal()">
                                    <i class="fas fa-plus"></i> 新增任务
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="processTaskSummaryTable" class="expandable-table">
                                <thead>
                                    <tr>
                                        <th width="40px"></th>
                                        <th width="100px">合同编号</th>
                                        <th width="100px">订单编号</th>
                                        <th width="80px">订单类型</th>
                                        <th width="100px">任务编号</th>
                                        <th width="80px">合同金额</th>
                                        <th width="80px">已分派金额</th>
                                        <th width="120px">所属机构</th>
                                        <th width="60px">部门</th>
                                        <th width="80px">科组</th>
                                        <th width="80px">任务状态</th>
                                        <th width="80px">工效比</th>
                                        <th width="100px">创建时间</th>
                                        <th width="80px">负责人</th>
                                        <th width="120px">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="processTaskSummaryTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 价值工时 -->
                <div id="value-hour" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">价值工时</h1>
                    </div>
                    <!-- 任务总表 -->
                    <div class="card" id="taskSummaryCard">
                        <div class="card-header">
                            <h3 class="card-title">任务总表</h3>
                            <div class="card-actions" style="display: flex; gap: 8px;">
                                <button id="addSummaryTaskBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增总任务
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="taskSummaryTable">
                                <thead>
                                    <tr>
                                        <th>任务号</th>
                                        <th>费率</th>
                                        <th>任务金额</th>
                                        <th>计划工时</th>
                                        <th>理论价值工时</th>
                                        <th>填报工时汇总</th>
                                        <th>确认价值工时</th>
                                        <th>交通费用</th>
                                        <th>住宿费用</th>
                                        <th>其他费用</th>
                                        <th>操作</th>
                                        <th>查看</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>AA5634</td>
                                        <td>500</td>
                                        <td>10000</td>
                                        <td>20</td>
                                        <td>18</td>
                                        <td>19</td>
                                        <td>17</td>
                                        <td>500</td>
                                        <td>800</td>
                                        <td>200</td>
                                        <td>
                                            <button class="action-btn edit-btn" onclick="editSummaryTask(this)">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="action-btn delete-btn" onclick="deleteSummaryTask(this)">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </td>
                                        <td>
                                            <button class="btn btn-outline" onclick="viewProjectTasks()">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- 项目任务情况表格 -->
                    <div class="card" id="projectCard" style="display:none;">
                        
                        <div class="card-header">
                            
                            <h3 class="card-title">项目任务情况</h3>
                            <div class="project-actions" style="display: flex; align-items: center; gap: 12px;">
                                <div>
                                    <button class="btn btn-outline small-btn" onclick="backToSummary()">返回</button>
                                </div>
                                <div class="card-actions" style="display: flex;">
                                    <button id="addProjectBtn" class="btn btn-success">
                                        <i class="fas fa-plus"></i> 新增项目
                                    </button>
                                </div>
                            </div>
                            <button class="action-btn" style="position: absolute; top: 18px; right: 18px;">
                                <i class="fas fa-sync-alt"></i>
                            </button>   
                        </div>
                        <div class="card-body">
                            <table id="projectTable">
                                <thead>
                                    <tr>
                                        <th>合同编号</th>
                                        <th>订单编号</th>
                                        <th>订单类型</th>
                                        <th>订单金额</th>
                                        <th>已分配订单金额</th>
                                        <th>部门</th>
                                        <th>科组</th>
                                        <th>标准工时</th>
                                        <th>功效比</th>
                                        <th>订单状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                        <th>查看</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>CD2019051335SX</td>
                                        <td>17397611</td>
                                        <td>服务合同</td>
                                        <td>24862.5</td>
                                        <td>11934</td>
                                        <td>西部事业部</td>
                                        <td>成都区域</td>
                                        <td>50</td>
                                        <td>98.66%</td>
                                        <td>已封闭</td>
                                        <td>2019-05-27</td>
                                        <td>
                                            <button class="action-btn edit-btn" onclick="editProjectRow(this)"><br>
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="action-btn delete-btn" onclick="deleteProjectRow(this)">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </td>
                                        <td>
                                            <button class="btn btn-outline" onclick="showProjectTasks('CD2019051335SX')">查看</button>
                                        </td>
                                    </tr>
                                    <tr onclick="showProjectTasks('CD2019052140XX')">
                                        <td>CD2019052140XX</td>
                                        <td>17401399</td>
                                        <td>有偿</td>
                                        <td>50222.25</td>
                                        <td>11934</td>
                                        <td>西部事业部</td>
                                        <td>成都区域</td>
                                        <td>101</td>
                                        <td>99.06%</td>
                                        <td>未封闭</td>
                                        <td>2019-05-24</td>
                                        <td>
                                            <button class="action-btn edit-btn" onclick="editProjectRow(this)"><br>
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="action-btn delete-btn" onclick="deleteProjectRow(this)">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </td>
                                        <td>
                                            <button class="btn btn-outline" onclick="showProjectTasks('CD2019052140XX')">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- 任务信息 -->
                    <div class="card" id="taskCard" style="display:none;">
                        <div class="card-header">
                            <h3 class="card-title">任务信息 <span id="currentProjectId" style="color:#007bff"></span></h3>
                            <div class="header-actions">
                                <div>
                                    <button class="btn btn-outline small-btn" onclick="backToProject()">返回</button>
                                </div>
                                <!-- <div>
                                    <button class="btn btn-outline" onclick="openRewardConfigModal('task')">奖惩系数设置</button>
                                </div> -->
                                <div class="card-actions">
                                    <button id="addTaskBtn" class="btn btn-success">
                                        <i class="fas fa-plus"></i> 新增任务
                                    </button>
                                </div>
                            </div>
                            <button class="action-btn" style="position: absolute; top: 18px; right: 18px;">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="taskTableWrapper">
                                <table id="taskTable">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>任务编号</th>
                                            <th>费率</th>
                                            <th>员工编号</th>
                                            <th>员工姓名</th>
                                            <th>所属部门</th>
                                            <th>科组</th>
                                            <th>任务金额</th>
                                            <th>计划工时</th>
                                            <th>增仓价款工时</th>
                                            <th>物理工时</th>
                                            <th>价值工时</th>
                                            <th>确认工时</th>
                                            <th>消耗费用</th>
                                            <th>Re-CoverRate</th>
                                            <th>工时利用率</th>
                                            <!-- <th>奖惩系数</th> -->
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                    <!-- 这一块的静态案例代码在js中 -->
                                </table>
                                <div class="order-notes">
                                    <p><strong>订单备注：</strong> 年度服务合同</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价值工时汇总 -->
                <div id="value-hour-add" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">价值工时汇总</h1>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">工时汇总报表（服务总监、经理权限）</h3>
                            <div class="header-actions">
                                <div class="card-actions">
                                    <button id="addWorkHourBtn" class="btn btn-success">
                                        <i class="fas fa-plus"></i> 新增任务
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="workHourSummaryTable" class="summary-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>员工编号</th>
                                        <th>员工姓名</th>
                                        <th>部门</th>
                                        <th>科组</th>
                                        <th>工时月份</th>
                                        <th>标准工时</th>
                                        <th>实际工时</th>
                                        <th>加班工时</th>
                                        <th>请假工时</th>
                                        <th>价值工时</th>
                                        <th>工时利用率</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody> </tbody>
                            </table>
                            <div style="width:100%;max-width:1400px;margin:auto; height:600px"> 
                                <canvas id="workHourBarChart" style="display: block; height: 120px; width: 100%"></canvas>
                            </div>
                            <!-- <canvas id="workHourBarChart" height="180" style="margin-top:32px;max-width:100%;"></canvas> -->
                        </div>
                    </div>
                </div>

                <!-- 产值汇总统计 -->
                <div id="output-add" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">产值汇总统计</h1>
                    </div>
                    <!-- 产值汇总统计明细表 -->
                    <div class="card" style="margin-top:24px;">
                        <div class="card-header">
                            <h3 class="card-title">产值汇总统计明细（GM、服务总监权限）</h3>
                            <div class="card-actions" style="display: flex;">
                                <button id="addOutputSummaryBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增产值
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="outputSummaryTable" class="summary-table">
                                <thead>
                                    <tr>
                                        <th>季/月度</th>
                                        <th>已分派产值</th>
                                        <th>已确认产值</th>
                                        <th>剩余金额</th>
                                        <th>已封闭产值</th>
                                        <th>BD金额</th>
                                        <th>BD剩余</th>
                                        <th>战略计提金额</th>
                                        <th>战略计提剩余</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态渲染内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 季度产值统计表 -->
                    <div class="card" style="margin-top:24px;">
                        <div class="card-header">
                            <h3 class="card-title">季度产值统计</h3>
                            <div class="card-actions" style="display: flex;">
                                <button id="addQuarterlyOutputBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增季度产值
                                </button>
                            </div>
                        </div>

                        <!-- 搜索筛选区域 -->
                        <div class="card-body" style="padding-bottom: 0;">
                            <div class="search-filters" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <div class="filter-group">
                                    <label>订单编号</label>
                                    <input type="text" id="filterOrderId" class="form-control" placeholder="请输入订单编号">
                                </div>
                                <div class="filter-group">
                                    <label>任务编号</label>
                                    <input type="text" id="filterTaskId" class="form-control" placeholder="请输入任务编号">
                                </div>
                                <div class="filter-group">
                                    <label>责任人</label>
                                    <input type="text" id="filterResponsible" class="form-control" placeholder="请输入责任人">
                                </div>
                                <div class="filter-group">
                                    <label>订单类型</label>
                                    <select id="filterOrderType" class="form-control">
                                        <option value="">全部</option>
                                        <option value="服务类">服务类</option>
                                        <option value="产品类">产品类</option>
                                        <option value="咨询类">咨询类</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>任务状态</label>
                                    <select id="filterTaskStatus" class="form-control">
                                        <option value="">全部</option>
                                        <option value="进行中">进行中</option>
                                        <option value="已完成">已完成</option>
                                        <option value="已暂停">已暂停</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>机构</label>
                                    <select id="filterInstitution" class="form-control">
                                        <option value="">全部</option>
                                        <option value="总部">总部</option>
                                        <option value="分公司">分公司</option>
                                        <option value="子公司">子公司</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>部门</label>
                                    <select id="filterDepartment" class="form-control">
                                        <option value="">全部</option>
                                        <option value="技术部">技术部</option>
                                        <option value="销售部">销售部</option>
                                        <option value="运营部">运营部</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>科组</label>
                                    <select id="filterGroup" class="form-control">
                                        <option value="">全部</option>
                                        <option value="开发组">开发组</option>
                                        <option value="测试组">测试组</option>
                                        <option value="运维组">运维组</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>年度筛选</label>
                                    <select id="filterYear" class="form-control">
                                        <option value="2024">2024年</option>
                                        <option value="2025">2025年</option>
                                        <option value="2023">2023年</option>
                                    </select>
                                </div>
                                <div class="filter-group" style="display: flex; align-items: end;">
                                    <button id="searchBtn" class="btn btn-primary" style="width: 100%;">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <table id="quarterlyOutputTable" class="summary-table">
                                <thead>
                                    <tr>
                                        <th>季度</th>
                                        <th>已分派产值</th>
                                        <th>已确认产值</th>
                                        <th>剩余产值</th>
                                        <th>已封闭产值</th>
                                        <th>未封闭产值</th>
                                        <th>已确认费用</th>
                                        <th>BD资金</th>
                                        <th>BD剩余</th>
                                        <th>战略计提</th>
                                        <th>工效比</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态渲染内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 个人奖金 -->
                <div id="personal-reward" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">个人奖金</h1>
                    </div>
                    <!-- 员工信息表 -->
                    <div class="card" style="margin-bottom: 18px;">
                        <div class="card-header">
                            <h3 class="card-title">员工信息</h3>
                        </div>
                        <div class="card-body">
                            <table class="summary-table">
                                <thead>
                                    <tr>
                                        <th>员工编号</th>
                                        <th>员工姓名</th>
                                        <th>所属部门</th>
                                        <th>所属科组</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>10001</td>
                                        <td>张三</td>
                                        <td>西部事业部</td>
                                        <td>成都区域</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 奖金详情表 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">奖金详情</h3>
                            <div class="time-selector" id="bonusTimeSelector">
                                <button class="time-btn active" data-time-type="monthly">月度</button>
                                <button class="time-btn" data-time-type="quarterly">季度</button>
                                <button class="time-btn" data-time-type="yearly">年度</button>
                            </div>
                        </div>
                    
                        <div class="card-body">
                            <table class="summary-table">
                                <thead>
                                    <tr>
                                        <th>年度</th>
                                        <th>月份</th>
                                        <th>确认价值工时</th>
                                        <th>所处档位</th>
                                        <th>标准工时</th>
                                        <th>预支奖</th>
                                        <th>AFM提成</th>
                                        <th>ASP</th>
                                        <th>调整工资</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2025</td>
                                        <td>01月</td>
                                        <td>280</td>
                                        <td>B档</td>
                                        <td>300</td>
                                        <td>¥4,500</td>
                                        <td>¥1,000</td>
                                        <td>¥600</td>
                                        <td>¥300</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 我的价值工时 -->
                <div id="my-value-hours" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">我的价值工时</h1>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">价值工时记录</h3>
                            <div class="card-actions">
                                <button id="addValueHourBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增任务
                                </button>
                            </div>
                        </div>

                        <div class="card-body">
                            <table id="valueHoursTable" class="summary-table">
                                <thead>
                                    <tr>
                                        <th>任务编号</th>
                                        <th>任务描述</th>
                                        <th>任务金额</th>
                                        <th>计划工时</th>
                                        <th>申报工时</th>
                                        <th>单价价值工时</th>
                                        <th>确认价值工时</th>
                                        <th>任务状态</th>
                                        <th>交通费</th>
                                        <th>住宿费</th>
                                        <th>其他费用</th>
                                        <th>工效比</th>
                                        <th>补贴系数</th>
                                        <th>合同属性</th>
                                        <th>负责人</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>AACN27212</td>
                                        <td>运营服务、专业飞行、市场开发</td>
                                        <td>7000</td>
                                        <td>18</td>
                                        <td>18</td>
                                        <td>14</td>
                                        <td>15</td>
                                        <td>未封闭</td>
                                        <td>0.00</td>
                                        <td>0.00</td>
                                        <td>0.00</td>
                                        <td>93.45%</td>
                                        <td>1</td>
                                        <td>南京分中心</td>
                                        <td>王勇</td>
                                        <td>7000</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editValueHourRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                                            <button class="action-btn delete-btn" onclick="deleteValueHourRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>AACN27223</td>
                                        <td>运营服务、专业飞行、市场开发</td>
                                        <td>12500</td>
                                        <td>30</td>
                                        <td>33</td>
                                        <td>25</td>
                                        <td>25</td>
                                        <td>未封闭</td>
                                        <td>1020.00</td>
                                        <td>350.00</td>
                                        <td>200.00</td>
                                        <td>87.44%</td>
                                        <td>1</td>
                                        <td>南京分中心</td>
                                        <td>王勇</td>
                                        <td>12500</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editValueHourRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                                            <button class="action-btn delete-btn" onclick="deleteValueHourRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>ABCN66532</td>
                                        <td>运营服务、专业飞行、市场开发</td>
                                        <td>16000</td>
                                        <td>35</td>
                                        <td>40</td>
                                        <td>32</td>
                                        <td>32</td>
                                        <td>已封闭</td>
                                        <td>834.00</td>
                                        <td>700.00</td>
                                        <td>400.00</td>
                                        <td>87.91%</td>
                                        <td>1</td>
                                        <td>南京分中心</td>
                                        <td>王教授</td>
                                        <td>16000</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editValueHourRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                                            <button class="action-btn delete-btn" onclick="deleteValueHourRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>BBN2453</td>
                                        <td>运营服务、专业飞行、市场开发</td>
                                        <td>11200</td>
                                        <td>25</td>
                                        <td>25</td>
                                        <td>22.4</td>
                                        <td>22</td>
                                        <td>未封闭</td>
                                        <td>220</td>
                                        <td>300</td>
                                        <td>185</td>
                                        <td>93.71%</td>
                                        <td>1.4</td>
                                        <td>南京分中心</td>
                                        <td>郑师兄</td>
                                        <td>8000</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editValueHourRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                                            <button class="action-btn delete-btn" onclick="deleteValueHourRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>AACN27234</td>
                                        <td>运营服务、专业飞行、市场开发</td>
                                        <td>14400</td>
                                        <td>35</td>
                                        <td>20</td>
                                        <td>28.8</td>
                                        <td>12</td>
                                        <td>未封闭</td>
                                        <td>660</td>
                                        <td>350</td>
                                        <td>180</td>
                                        <td>45.42%</td>
                                        <td>1</td>
                                        <td>南京分中心</td>
                                        <td>王勇</td>
                                        <td>14400</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editValueHourRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                                            <button class="action-btn delete-btn" onclick="deleteValueHourRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>AACN27245</td>
                                        <td>运营服务、专业飞行、市场开发</td>
                                        <td>18760</td>
                                        <td>40</td>
                                        <td>28</td>
                                        <td>37.52</td>
                                        <td>10</td>
                                        <td>未封闭</td>
                                        <td>100</td>
                                        <td>245</td>
                                        <td>180</td>
                                        <td>27.42%</td>
                                        <td>1</td>
                                        <td>南京分中心</td>
                                        <td>王勇</td>
                                        <td>18760</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editValueHourRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                                            <button class="action-btn delete-btn" onclick="deleteValueHourRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 统计汇总 -->
                <div id="statistical-summary" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">统计汇总</h1>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-title">
                                <i class="fas fa-users"></i> 员工总数
                            </div>
                            <div class="stat-value">128</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-title">
                                <i class="fas fa-project-diagram"></i> 进行中项目
                            </div>
                            <div class="stat-value">24</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-title">
                                <i class="fas fa-money-bill-wave"></i> 本月总奖金
                            </div>
                            <div class="stat-value">¥368,500</div>
                        </div>
                    </div>
                    
                    <!-- 筛选条件 -->
                    <div class="card" style="margin-top:24px; margin-bottom: 20px;">
                        <div class="card-header">
                            <h3 class="card-title">筛选条件</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; gap: 20px; align-items: center;">
                                <div class="form-group" style="min-width: 150px;">
                                    <label for="statDepartmentFilter">部门</label>
                                    <select id="statDepartmentFilter" class="form-control">
                                        <option value="">全部部门</option>
                                        <option value="西部事业部">西部事业部</option>
                                        <option value="华南事业群">华南事业群</option>
                                        <option value="华东事业群">华东事业群</option>
                                        <option value="北方事业群">北方事业群</option>
                                    </select>
                                </div>
                                <div class="form-group" style="min-width: 150px;">
                                    <label for="statGroupFilter">科组</label>
                                    <select id="statGroupFilter" class="form-control">
                                        <option value="">全部科组</option>
                                        <option value="成都区域">成都区域</option>
                                        <option value="重庆区域">重庆区域</option>
                                        <option value="广州区域">广州区域</option>
                                        <option value="深圳区域">深圳区域</option>
                                        <option value="上海区域">上海区域</option>
                                        <option value="杭州区域">杭州区域</option>
                                        <option value="北京区域">北京区域</option>
                                        <option value="天津区域">天津区域</option>
                                    </select>
                                </div>
                                <div>
                                    <button id="searchStatBtn" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计汇总奖金明细表 -->
                    <div class="card">
                        <div class="card-header" style="display:flex;justify-content:space-between;align-items:center;">
                            <h3 class="card-title">奖金统计明细</h3>
                            <div class="time-selector" id="statTimeSelector">
                                <button class="time-btn active" data-time-type="monthly">月度</button>
                                <button class="time-btn" data-time-type="quarterly">季度</button>
                                <button class="time-btn" data-time-type="yearly">年度</button>
                            </div>
                            <div>
                                <button class="btn btn-outline" onclick="openRewardConfigModal('stat')">奖惩系数设置</button>
                            </div>
                            <div class="card-actions">
                                <button id="addStatBonusBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="statSummaryBonusTable" class="summary-table">
                                <thead>
                                    <tr>
                                        <th>员工编号</th>
                                        <th>员工姓名</th>
                                        <th>部门</th>
                                        <th>科组</th>
                                        <th id="statPeriodHeader">月份</th>
                                        <th>基本工资</th>
                                        <th>价值工时</th>
                                        <th>奖惩系数</th>
                                        <th>基本奖金</th>
                                        <th>绩效奖金</th>
                                        <th>总计</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <div style="width:100%;max-width:1400px;margin:auto; height:600px"> 
                                <canvas id="statBonusStackedBarChart" style="display: block; height: 120px; width: 100%"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标准工时配置 -->
                <div id="statitic-hour" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">标准工时配置</h1>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">工时标准设置（HR修改权限；GM、服务总监查阅权限）</h3>
                            <div class="card-actions" style="display: flex;">
                                <button id="addStandardTimesBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增标准
                                </button>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <table id="standardHoursTable">
                                <thead>
                                    <tr>
                                        <th>任务类型</th>
                                        <th>标准工时(小时)</th>
                                        <th>费率(元/小时)</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>需求分析</td>
                                        <td>8</td>
                                        <td>450</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editStandardTimesRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                                            <button class="action-btn delete-btn" onclick="deleteStandardTimesRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>方案设计</td>
                                        <td>16</td>
                                        <td>500</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editStandardTimesRow(this)"><i class="fas fa-edit"></i> 编辑</button>
                                            <button class="action-btn delete-btn" onclick="deleteStandardTimesRow(this)"><i class="fas fa-trash-alt"></i> 删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 工资档位配置 -->
                <div id="wage-units" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">工资档位配置</h1>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">档位设置（HR修改权限；GM、服务总监查阅权限）</h3>
                            <div class="card-actions">
                                <button id="addWageUnitBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="wageUnitsTable">
                                <thead>
                                    <tr>
                                        <th>工资档位</th>
                                        <th>预支奖</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>8000</td>
                                        <td>¥5,000</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editWageUnitRow(this)">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="action-btn delete-btn" onclick="deleteWageUnitRow(this)">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>12000</td>
                                        <td>¥4,500</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editWageUnitRow(this)">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="action-btn delete-btn" onclick="deleteWageUnitRow(this)">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>15000</td>
                                        <td>¥4,000</td>
                                        <td class="action-cell">
                                            <button class="action-btn edit-btn" onclick="editWageUnitRow(this)">
                                                <i class="fas fa-edit"></i> 编辑
                                            </button>
                                            <button class="action-btn delete-btn" onclick="deleteWageUnitRow(this)">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 年度资金分配 -->
                <div id="annual-fund" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">年度资金分配</h1>
                    </div>
                    
                    <!-- 筛选条件 -->
                    <div class="card" style="margin-bottom: 20px;">
                        <div class="card-header">
                            <h3 class="card-title">筛选条件</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; gap: 20px; align-items: center;">
                                <div class="form-group" style="min-width: 150px;">
                                    <label for="annualYearFilter">数据年度</label>
                                    <select id="annualYearFilter" class="form-control">
                                        <option value="">全部年度</option>
                                        <option value="2025">2025年</option>
                                        <option value="2024">2024年</option>
                                        <option value="2023">2023年</option>
                                    </select>
                                </div>
                                <div>
                                    <button id="searchAnnualBtn" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">年度资金分配明细</h3>
                            <div class="card-actions">
                                <button id="addAnnualFundBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增分配
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="annualFundTable" class="summary-table">
                                <thead>
                                    <tr>
                                        <th>数据年度</th>
                                        <th>事业部名称</th>
                                        <th>年度BD金额</th>
                                        <th>战略计提金额</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态渲染内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 年度资金分配模态框 -->
                <div id="annualFundModal" class="modal" style="display:none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title" id="annualFundModalTitle">新增年度资金分配</h3>
                            <button class="close-modal" onclick="closeAnnualFundModal()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <div class="form-row">
                                <label for="annualFundYear">数据年度</label>
                                <select id="annualFundYear" class="form-control">
                                    <option value="2025">2025年</option>
                                    <option value="2024">2024年</option>
                                    <option value="2023">2023年</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <label for="annualFundBusinessUnit">事业部名称</label>
                                <input type="text" id="annualFundBusinessUnit" class="form-control">
                            </div>
                            <div class="form-row">
                                <label for="annualFundBdAmount">年度BD金额</label>
                                <input type="number" id="annualFundBdAmount" class="form-control">
                            </div>
                            <div class="form-row">
                                <label for="annualFundStrategicAmount">战略计提金额</label>
                                <input type="number" id="annualFundStrategicAmount" class="form-control">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-outline" onclick="closeAnnualFundModal()">取消</button>
                            <button class="btn" id="saveAnnualFundBtn">保存</button>
                        </div>
                    </div>
                </div>

                <!-- 地区战略分配 -->
                <div id="regional-strategic" style="display:none;">
                    <div class="page-header">
                        <h1 class="page-title">地区战略分配</h1>
                    </div>
                    
                    <!-- 筛选条件 -->
                    <div class="card" style="margin-bottom: 20px;">
                        <div class="card-header">
                            <h3 class="card-title">筛选条件</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; gap: 20px; align-items: end;">
                                <div class="form-group" style="min-width: 150px;">
                                    <label for="regionFilter">地区</label>
                                    <select id="regionFilter" class="form-control">
                                        <option value="">全部地区</option>
                                        <option value="华北地区">华北地区</option>
                                        <option value="华东地区">华东地区</option>
                                        <option value="华南地区">华南地区</option>
                                        <option value="西部地区">西部地区</option>
                                        <option value="东北地区">东北地区</option>
                                    </select>
                                </div>
                                <div class="form-group" style="min-width: 150px;">
                                    <label for="yearFilter">数据年度</label>
                                    <select id="yearFilter" class="form-control">
                                        <option value="">全部年度</option>
                                        <option value="2025">2025年</option>
                                        <option value="2024">2024年</option>
                                        <option value="2023">2023年</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <button id="searchRegionalBtn" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">地区战略分配明细</h3>
                            <div class="card-actions">
                                <button id="addRegionalBtn" class="btn btn-success">
                                    <i class="fas fa-plus"></i> 新增分配
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="regionalStrategicTable" class="summary-table">
                                <thead>
                                    <tr>
                                        <th>数据年度</th>
                                        <th>地区</th>
                                        <th>BD比例</th>
                                        <th>BD金额</th>
                                        <th>战略计提比例</th>
                                        <th>战略计提金额</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 动态渲染内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 新增/编辑任务模态框 -->
    <div id="taskModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增任务</h3>
                <button class="close-modal" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label for="taskId">任务编号</label>
                    <input type="text" id="taskId" class="form-control">
                </div>
                <div class="form-row">
                    <label for="feeRate">费率</label>
                    <input type="number" id="feeRate" class="form-control">
                </div>
                <div class="form-row">
                    <label for="empId">员工编号</label>
                    <input type="text" id="empId" class="form-control">
                </div>
                <div class="form-row">
                    <label for="empName">员工姓名</label>
                    <input type="text" id="empName" class="form-control">
                </div>
                <div class="form-row">
                    <label for="department">所属部门</label>
                    <select id="department" class="form-control">
                        <option value="">请选择部门</option>
                        <option value="西部事业部" selected>西部事业部</option>
                        <option value="华南事业群">华南事业群</option>
                        <option value="华东事业群">华东事业群</option>
                        <option value="北方事业群">北方事业群</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="group">科组</label>
                    <input type="text" id="group" class="form-control" value="成都区域">
                </div>
                <div class="form-row">
                    <label for="taskAmount">任务金额</label>
                    <input type="number" id="taskAmount" class="form-control" value="3978.00">
                </div>
                <div class="form-row">
                    <label for="plannedHours">计划工时</label>
                    <input type="number" id="plannedHours" class="form-control" value="8">
                </div>
                <div class="form-row">
                    <label for="additionalHours">增仓价款工时</label>
                    <input type="number" id="additionalHours" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label for="extraHours">物理工时</label>
                    <input type="number" id="extraHours" class="form-control" step="0.1">
                </div>
                <div class="form-row">
                    <label for="valueHourDisplay">价值工时</label>
                    <input type="number" id="valueHourDisplay" class="form-control" readonly>
                </div>
                <div class="form-row">
                    <label for="confirmedHours">确认工时</label>
                    <input type="number" id="confirmedHours" class="form-control">
                </div>
                <div class="form-row">
                    <label for="cost">消耗费用</label>
                    <input type="number" id="cost" class="form-control">
                </div>
                <div class="form-row">
                    <label for="recoverRate">Re-CoverRate</label>
                    <input type="number" id="recoverRate" class="form-control" readonly>
                </div>
                <div class="form-row">
                    <label for="hourUtilization">工时利用率</label>
                    <input type="number" id="hourUtilization" class="form-control" readonly>
                </div>
                <!-- <div class="form-row">
                    <label for="rewardFactor">奖惩系数</label>
                    <input type="number" id="rewardFactor" class="form-control" readonly>
                </div> -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeModal()">取消</button>
                <button class="btn" id="saveTaskBtn">保存任务</button>
            </div>
        </div>
    </div>
    <!-- 任务总表 新增/编辑模态框 -->
    <div id="summaryTaskModal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="summaryModalTitle">新增总任务</h3>
                <button class="close-modal" onclick="closeSummaryModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row"><label>任务号</label><input type="text" id="summaryTaskId" class="form-control"></div>
                <div class="form-row"><label>费率</label><input type="number" id="summaryFeeRate" class="form-control"></div>
                <div class="form-row"><label>任务金额</label><input type="text" id="summaryAmount" class="form-control"></div>
                <div class="form-row"><label>计划工时</label><input type="number" id="summaryPlannedHours" class="form-control"></div>
                <div class="form-row"><label>理论价值工时</label><input type="number" id="summaryTheoryHours" class="form-control"></div>
                <div class="form-row"><label>填报工时汇总</label><input type="number" id="summaryReportedHours" class="form-control"></div>
                <div class="form-row"><label>确认价值工时</label><input type="number" id="summaryConfirmedHours" class="form-control"></div>
                <div class="form-row"><label>交通费用</label><input type="text" id="summaryTrafficFee" class="form-control"></div>
                <div class="form-row"><label>住宿费用</label><input type="text" id="summaryHotelFee" class="form-control"></div>
                <div class="form-row"><label>其他费用</label><input type="text" id="summaryOtherFee" class="form-control"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeSummaryModal()">取消</button>
                <button class="btn" id="saveSummaryTaskBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 项目任务表 新增/编辑模态框 -->
    <div id="projectModal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="projectModalTitle">新增项目</h3>
                <button class="close-modal" onclick="closeProjectModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row"><label>合同编号</label><input type="text" id="projectContractId" class="form-control"></div>
                <div class="form-row"><label>订单编号</label><input type="text" id="projectOrderId" class="form-control"></div>
                <div class="form-row"><label>订单类型</label><input type="text" id="projectOrderType" class="form-control"></div>
                <div class="form-row"><label>订单金额</label><input type="text" id="projectOrderAmount" class="form-control"></div>
                <div class="form-row"><label>已分配订单金额</label><input type="text" id="projectAllocatedAmount" class="form-control"></div>
                <div class="form-row"><label>部门</label><input type="text" id="projectDepartment" class="form-control"></div>
                <div class="form-row"><label>科组</label><input type="text" id="projectGroup" class="form-control"></div>
                <div class="form-row"><label>标准工时</label><input type="number" id="projectStandardHours" class="form-control"></div>
                <div class="form-row"><label>功效比</label><input type="text" id="projectEfficiency" class="form-control"></div>
                <div class="form-row"><label>订单状态</label><input type="text" id="projectOrderStatus" class="form-control"></div>
                <div class="form-row"><label>创建时间</label><input type="text" id="projectCreateTime" class="form-control"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeProjectModal()">取消</button>
                <button class="btn" id="saveProjectBtn">保存</button>
            </div>
        </div>
    </div>
        <!-- 奖惩系数设置模态框 -->
    <div id="rewardConfigModal" class="modal" style="display:none;">
    <div class="modal-content" style="width:400px;">
        <div class="modal-header">
        <h3>奖惩系数区间设置</h3>
        <button onclick="closeRewardConfigModal()" style="float:right;">×</button>
        </div>
        <div class="modal-body">
        <div class="form-row">
            <label>基准值</label>
            <input type="number" id="rewardBaseValue" class="form-control" value="400">
        </div>
        <div class="form-row">
            <label>小于区间阈值</label>
            <input type="number" id="rewardMinLimit" class="form-control" value="200">
            <label>系数</label>
            <input type="number" id="rewardMinFactor" class="form-control" value="0" step="0.01">
        </div>
        <div class="form-row">
            <label>大于等于区间阈值</label>
            <input type="number" id="rewardMaxLimit" class="form-control" value="900">
            <label>系数</label>
            <input type="number" id="rewardMaxFactor" class="form-control" value="1.5" step="0.01">
        </div>
        <div id="rewardRanges">
            <!-- 区间输入框会自动渲染 -->
        </div>
        <button class="btn btn-outline" onclick="addRewardRange()">+ 添加区间</button>
        </div>
        <div class="modal-footer">
        <button class="btn btn-outline" onclick="closeRewardConfigModal()">取消</button>
        <button class="btn btn-success" onclick="saveRewardConfig()">保存</button>
        </div>
    </div>
    </div>
    <div id="workHourModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:400px;">
            <div class="modal-header">
                <h3 class="modal-title" id="workHourModalTitle">新增工时</h3>
                <button class="close-modal" onclick="closeWorkHourModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row"><label>员工编号</label><input type="text" id="whEmpId" class="form-control"></div>
                <div class="form-row"><label>员工姓名</label><input type="text" id="whEmpName" class="form-control"></div>
                <div class="form-row"><label>部门</label><input type="text" id="whDepartment" class="form-control"></div>
                <div class="form-row"><label>科组</label><input type="text" id="whGroup" class="form-control"></div>
                <div class="form-row"><label>工时月份</label><input type="text" id="whMonth" class="form-control"></div>
                <div class="form-row"><label>标准工时</label><input type="number" id="whStandard" class="form-control"></div>
                <div class="form-row"><label>实际工时</label><input type="number" id="whActual" class="form-control"></div>
                <div class="form-row"><label>加班工时</label><input type="number" id="whOvertime" class="form-control"></div>
                <div class="form-row"><label>请假工时</label><input type="number" id="whLeave" class="form-control"></div>
                <div class="form-row"><label>价值工时</label><input type="number" id="whValue" class="form-control"></div>
                <div class="form-row"><label>工时利用率</label><input type="text" id="whUtilization" class="form-control" readonly></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeWorkHourModal()">取消</button>
                <button class="btn" id="saveWorkHourBtn">保存</button>
            </div>
        </div>
    </div>
    <!-- 奖金明细 新增/编辑模态框 -->
    <div id="statBonusModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:400px;">
            <div class="modal-header">
                <h3 class="modal-title" id="statBonusModalTitle">新增奖金明细</h3>
                <button class="close-modal" onclick="closeStatBonusModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row"><label>员工编号</label><input type="text" id="statEmpId" class="form-control"></div>
                <div class="form-row"><label>员工姓名</label><input type="text" id="statEmpName" class="form-control"></div>
                <div class="form-row"><label>部门</label><input type="text" id="statDepartment" class="form-control"></div>
                <div class="form-row"><label>科组</label><input type="text" id="statGroup" class="form-control"></div>
                <div class="form-row"><label id="statPeriodLabel">月份</label><input type="text" id="statMonth" class="form-control"></div>
                <div class="form-row"><label>基本工资</label><input type="number" id="statBaseSalary" class="form-control"></div>
                <div class="form-row"><label>价值工时</label><input type="number" id="statValueHour" class="form-control"></div>
                <div class="form-row"><label>奖惩系数</label><input type="number" id="statRewardFactor" class="form-control" step="0.01" readonly></div>
                <div class="form-row"><label>基本奖金</label><input type="number" id="statBaseBonus" class="form-control"></div>
                <div class="form-row"><label>绩效奖金</label><input type="number" id="statPerformanceBonus" class="form-control" readonly></div>
                <div class="form-row"><label>总计</label><input type="number" id="statTotal" class="form-control" readonly></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeStatBonusModal()">取消</button>
                <button class="btn" id="saveStatBonusBtn">保存</button>
            </div>
        </div>
    </div>
    <!-- 新增流程任务模态框 -->
    <div id="processTaskModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:400px;">
            <div class="modal-header">
            <h3 class="modal-title" id="processTaskModalTitle">新增流程任务</h3>
            <button class="close-modal" onclick="closeProcessTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
            <div class="form-row">
                <label>合同编号</label>
                <input type="text" id="processContractId" class="form-control">
            </div>
            <div class="form-row">
                <label>订单编号</label>
                <input type="text" id="processOrderId" class="form-control">
            </div>
            <div class="form-row">
                <label>订单类型</label>
                <input type="text" id="processCertType" class="form-control">
            </div>
            <div class="form-row">
                <label>任务编号</label>
                <input type="text" id="processTaskId" class="form-control">
            </div>
            <div class="form-row">
                <label>合同金额</label>
                <input type="number" id="processContractAmount" class="form-control">
            </div>
            <div class="form-row">
                <label>已分派金额</label>
                <input type="number" id="processReceivedAmount" class="form-control">
            </div>
            <div class="form-row">
                <label>所属机构</label>
                <input type="text" id="processOrganization" class="form-control">
            </div>
            <div class="form-row">
                <label>部门</label>
                <input type="text" id="processFlow" class="form-control">
            </div>
            <div class="form-row">
                <label>科组</label>
                <input type="text" id="processGroup" class="form-control">
            </div>
            <div class="form-row">
                <label>任务状态</label>
                <select id="processTaskStatus" class="form-control">
                    <option value="未封闭">未封闭</option>
                    <option value="已封闭">已封闭</option>
                </select>
            </div>
            <div class="form-row">
                <label>工效比</label>
                <input type="text" id="processWorkRatio" class="form-control">
            </div>
            <div class="form-row">
                <label>创建时间</label>
                <input type="text" id="processCreateTime" class="form-control">
            </div>
            <div class="form-row">
                <label>负责人</label>
                <input type="text" id="processResponsible" class="form-control">
            </div>
            </div>
            <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeProcessTaskModal()">取消</button>
            <button class="btn" id="saveProcessTaskBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 一级流程模态框 -->
    <div id="firstLevelProcessModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:400px;">
            <div class="modal-header">
                <h3 class="modal-title" id="firstLevelProcessModalTitle">新增一级流程</h3>
                <button class="close-modal" onclick="closeFirstLevelProcessModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>流程编号</label>
                    <input type="text" id="firstLevelProcessId" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务名称</label>
                    <input type="text" id="firstLevelTaskName" class="form-control">
                </div>
                <div class="form-row">
                    <label>负责部门</label>
                    <input type="text" id="firstLevelDepartment" class="form-control">
                </div>
                <div class="form-row">
                    <label>权重</label>
                    <input type="text" id="firstLevelWeight" class="form-control">
                </div>
                <div class="form-row">
                    <label>金额</label>
                    <input type="number" id="firstLevelAmount" class="form-control">
                </div>
                <div class="form-row">
                    <label>完成率</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <input type="range" id="firstLevelCompletionRate" class="form-control" min="0" max="100" value="0" style="flex: 1;" oninput="updateFirstLevelProgress(this.value)">
                        <span id="firstLevelProgressText" style="min-width: 40px; font-weight: bold;">0%</span>
                    </div>
                </div>
                <div class="form-row">
                    <label>任务完成情况</label>
                    <select id="firstLevelCompletionStatus" class="form-control">
                        <option value="未完成">未完成</option>
                        <option value="进行中">进行中</option>
                        <option value="已完成">已完成</option>
                        <option value="暂停">暂停</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>责任人</label>
                    <input type="text" id="firstLevelResponsible" class="form-control">
                </div>
                <div class="form-row">
                    <label>备注</label>
                    <textarea id="firstLevelRemark" class="form-control" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeFirstLevelProcessModal()">取消</button>
                <button class="btn" id="saveFirstLevelProcessBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 二级流程模态框 -->
    <div id="secondLevelProcessModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:450px;">
            <div class="modal-header">
                <h3 class="modal-title" id="secondLevelProcessModalTitle">新增二级流程</h3>
                <button class="close-modal" onclick="closeSecondLevelProcessModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>流程编号</label>
                    <input type="text" id="secondLevelProcessId" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务名称</label>
                    <input type="text" id="secondLevelTaskName" class="form-control">
                </div>
                <div class="form-row">
                    <label>负责部门</label>
                    <input type="text" id="secondLevelDepartment" class="form-control">
                </div>
                <div class="form-row">
                    <label>产值权重</label>
                    <input type="text" id="secondLevelValueWeight" class="form-control">
                </div>
                <div class="form-row">
                    <label>金额</label>
                    <input type="number" id="secondLevelAmount" class="form-control">
                </div>
                <div class="form-row">
                    <label>完成率</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <input type="range" id="secondLevelCompletionRate" class="form-control" min="0" max="100" value="0" style="flex: 1;" oninput="updateSecondLevelProgress(this.value)">
                        <span id="secondLevelProgressText" style="min-width: 40px; font-weight: bold;">0%</span>
                    </div>
                </div>
                <div class="form-row">
                    <label>责任人</label>
                    <input type="text" id="secondLevelResponsible" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务完成情况</label>
                    <select id="secondLevelCompletionStatus" class="form-control">
                        <option value="未完成">未完成</option>
                        <option value="进行中">进行中</option>
                        <option value="已完成">已完成</option>
                        <option value="暂停">暂停</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>备注</label>
                    <textarea id="secondLevelRemark" class="form-control" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeSecondLevelProcessModal()">取消</button>
                <button class="btn" id="saveSecondLevelProcessBtn">保存</button>
            </div>
        </div>
    </div>

<!-- 任务信息表 新增/编辑模态框（你已有的 taskModal 可继续用） -->
<!-- ...你已有的 #taskModal ... -->
<!-- 步骤操作模态框 -->
    <div id="stepModal" class="modal" style="display:none;">
    <div class="modal-content" style="width:320px;">
        <div class="modal-header">
        <h3 class="modal-title" id="stepModalTitle">编辑任务步骤</h3>
        <button class="close-modal" onclick="closeStepModal()">&times;</button>
        </div>
        <div class="modal-body">
        <div class="form-row">
            <label>任务名称</label>
            <input type="text" id="stepName" class="form-control">
        </div>
        <div class="form-row">
            <label>进度(%)</label>
            <input type="number" id="stepProgress" class="form-control" min="0" max="100">
        </div>
        </div>
        <div class="modal-footer">
        <button class="btn btn-outline" onclick="closeStepModal()">取消</button>
        <button class="btn btn-danger" id="deleteStepBtn" style="margin-right:auto;">删除</button>
        <button class="btn" id="saveStepBtn">保存</button>
        </div>
    </div>
    </div>
    <div id="standardHoursModel" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="standardHoursTitle">新增项目</h3>
                <button class="close-modal" onclick="closeStandardHoursModel()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row"><label>任务类型</label><input type="text" id="standardTaskType" class="form-control"></div>
                <div class="form-row"><label>标准工时（小时）</label><input type="text" id="standardHoursTime" class="form-control"></div>
                <div class="form-row"><label>费率（元/小时）</label><input type="text" id="standardFeeRate" class="form-control"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeStandardHoursModel()">取消</button>
                <button class="btn" id="saveStandardHoursBtn">保存</button>
            </div>
        </div>
    </div>
    <!-- 产值汇总统计 新增/编辑模态框 -->
    <div id="outputSummaryModal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="outputSummaryModalTitle">新增产值汇总</h3>
                <button class="close-modal" onclick="closeOutputSummaryModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row"><label>季/月度</label><input type="text" id="outputPeriod" class="form-control" placeholder="例如: 2025-Q1"></div>
                <div class="form-row"><label>已分派产值</label><input type="text" id="outputAssigned" class="form-control"></div>
                <div class="form-row"><label>已确认产值</label><input type="text" id="outputConfirmed" class="form-control"></div>
                <div class="form-row"><label>剩余金额</label><input type="text" id="outputRemaining" class="form-control"></div>
                <div class="form-row"><label>已封闭产值</label><input type="text" id="outputClosed" class="form-control"></div>
                <div class="form-row"><label>BD金额</label><input type="text" id="outputBdAmount" class="form-control"></div>
                <div class="form-row"><label>BD剩余</label><input type="text" id="outputBdRemaining" class="form-control"></div>
                <div class="form-row"><label>战略计提金额</label><input type="text" id="outputStrategicAmount" class="form-control"></div>
                <div class="form-row"><label>战略计提剩余</label><input type="text" id="outputStrategicRemaining" class="form-control"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeOutputSummaryModal()">取消</button>
                <button class="btn" id="saveOutputSummaryBtn">保存</button>
            </div>
        </div>
    </div>
    <!-- 季度产值 新增/编辑模态框 -->
    <div id="quarterlyOutputModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:600px;">
            <div class="modal-header">
                <h3 class="modal-title" id="quarterlyOutputModalTitle">新增季度产值</h3>
                <button class="close-modal" onclick="closeQuarterlyOutputModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>季度</label>
                    <input type="text" id="qoQuarter" class="form-control" placeholder="例如: 2024-Q1">
                </div>
                <div class="form-row">
                    <label>已分派产值</label>
                    <input type="number" id="qoAssignedOutput" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>已确认产值</label>
                    <input type="number" id="qoConfirmedOutput" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>剩余产值</label>
                    <input type="number" id="qoRemainingOutput" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>已封闭产值</label>
                    <input type="number" id="qoClosedOutput" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>未封闭产值</label>
                    <input type="number" id="qoUnclosedOutput" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>已确认费用</label>
                    <input type="number" id="qoConfirmedExpense" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>BD资金</label>
                    <input type="number" id="qoBdFund" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>BD剩余</label>
                    <input type="number" id="qoBdRemaining" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>战略计提</label>
                    <input type="number" id="qoStrategicProvision" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>工效比</label>
                    <input type="text" id="qoEfficiencyRatio" class="form-control" placeholder="例如: 79.34%">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeQuarterlyOutputModal()">取消</button>
                <button class="btn" id="saveQuarterlyOutputBtn">保存</button>
            </div>
        </div>
    </div>
    <!-- 工资档位配置 新增/编辑模态框 -->
    <div id="wageUnitModal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="wageUnitModalTitle">新增工资档位</h3>
                <button class="close-modal" onclick="closeWageUnitModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>工资档位</label>
                    <input type="number" id="wageUnitLevel" class="form-control" placeholder="请输入工资档位数字">
                </div>
                <div class="form-row">
                    <label>预支奖</label>
                    <input type="number" id="wageUnitAdvanceBonus" class="form-control" placeholder="请输入预支奖金额">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeWageUnitModal()">取消</button>
                <button class="btn" id="saveWageUnitBtn">保存</button>
            </div>
        </div>
    </div>
    <!-- 地区战略分配模态框 -->
    <div id="regionalStrategicModal" class="modal" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="regionalStrategicModalTitle">新增地区战略分配</h3>
                <button class="close-modal" onclick="closeRegionalStrategicModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label for="regionalYear">数据年度</label>
                    <select id="regionalYear" class="form-control">
                        <option value="2025">2025年</option>
                        <option value="2024">2024年</option>
                        <option value="2023">2023年</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="regionalRegion">地区</label>
                    <select id="regionalRegion" class="form-control">
                        <option value="华北地区">华北地区</option>
                        <option value="华东地区">华东地区</option>
                        <option value="华南地区">华南地区</option>
                        <option value="西部地区">西部地区</option>
                        <option value="东北地区">东北地区</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="regionalBdRatio">BD比例</label>
                    <input type="text" id="regionalBdRatio" class="form-control" placeholder="例如: 15%">
                </div>
                <div class="form-row">
                    <label for="regionalBdAmount">BD金额</label>
                    <input type="number" id="regionalBdAmount" class="form-control">
                </div>
                <div class="form-row">
                    <label for="regionalStrategicRatio">战略计提比例</label>
                    <input type="text" id="regionalStrategicRatio" class="form-control" placeholder="例如: 8%">
                </div>
                <div class="form-row">
                    <label for="regionalStrategicAmount">战略计提金额</label>
                    <input type="number" id="regionalStrategicAmount" class="form-control">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeRegionalStrategicModal()">取消</button>
                <button class="btn" id="saveRegionalStrategicBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 日历备注模态框 -->
    <div id="calendarNoteModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:400px;">
            <div class="modal-header">
                <h3 class="modal-title" id="calendarNoteModalTitle">日期备注</h3>
                <button class="close-modal" onclick="closeCalendarNoteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label id="calendarNoteDateLabel">日期</label>
                    <input type="text" id="calendarNoteDate" class="form-control" readonly>
                </div>
                <div class="form-row">
                    <label>工时</label>
                    <input type="text" id="calendarNoteHours" class="form-control" readonly>
                </div>
                <div class="form-row">
                    <label>备注</label>
                    <textarea id="calendarNoteContent" class="form-control" rows="4" placeholder="请输入备注内容..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeCalendarNoteModal()">取消</button>
                <button class="btn" id="saveCalendarNoteBtn">保存备注</button>
            </div>
        </div>
    </div>

    <!-- 个人任务模态框 -->
    <div id="personalTaskModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:400px;">
            <div class="modal-header">
                <h3 class="modal-title" id="personalTaskModalTitle">新增流程</h3>
                <button class="close-modal" onclick="closePersonalTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>名称</label>
                    <input type="text" id="personalTaskName" class="form-control" placeholder="请输入流程名称">
                </div>
                <div class="form-row">
                    <label>审核状态</label>
                    <select id="personalTaskStatus" class="form-control">
                        <option value="执行中">执行中</option>
                        <option value="提交">提交</option>
                        <option value="审核">审核</option>
                        <option value="审批">审批</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closePersonalTaskModal()">取消</button>
                <button class="btn" id="savePersonalTaskBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 价值工时 新增/编辑模态框 -->
    <div id="valueHourModal" class="modal" style="display:none;">
        <div class="modal-content" style="width:600px;">
            <div class="modal-header">
                <h3 class="modal-title" id="valueHourModalTitle">新增价值工时</h3>
                <button class="close-modal" onclick="closeValueHourModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label>任务编号</label>
                    <input type="text" id="vhTaskId" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务描述</label>
                    <input type="text" id="vhTaskDesc" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务金额</label>
                    <input type="number" id="vhTaskAmount" class="form-control">
                </div>
                <div class="form-row">
                    <label>计划工时</label>
                    <input type="number" id="vhPlannedHours" class="form-control">
                </div>
                <div class="form-row">
                    <label>申报工时</label>
                    <input type="number" id="vhReportedHours" class="form-control">
                </div>
                <div class="form-row">
                    <label>单价价值工时</label>
                    <input type="number" id="vhUnitValueHours" class="form-control">
                </div>
                <div class="form-row">
                    <label>确认价值工时</label>
                    <input type="number" id="vhConfirmedValueHours" class="form-control">
                </div>
                <div class="form-row">
                    <label>任务状态</label>
                    <select id="vhTaskStatus" class="form-control">
                        <option value="未封闭">未封闭</option>
                        <option value="已封闭">已封闭</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>交通费</label>
                    <input type="number" id="vhTransportFee" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>住宿费</label>
                    <input type="number" id="vhAccommodationFee" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>其他费用</label>
                    <input type="number" id="vhOtherFee" class="form-control" step="0.01">
                </div>
                <div class="form-row">
                    <label>工效比</label>
                    <input type="text" id="vhEfficiencyRatio" class="form-control" readonly>
                </div>
                <div class="form-row">
                    <label>补贴系数</label>
                    <input type="number" id="vhSubsidyCoeff" class="form-control" step="0.1">
                </div>
                <div class="form-row">
                    <label>合同属性</label>
                    <input type="text" id="vhContractAttr" class="form-control">
                </div>
                <div class="form-row">
                    <label>负责人</label>
                    <input type="text" id="vhResponsiblePerson" class="form-control">
                </div>
                <div class="form-row">
                    <label>备注</label>
                    <input type="text" id="vhRemark" class="form-control">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeValueHourModal()">取消</button>
                <button class="btn" id="saveValueHourBtn">保存</button>
            </div>
        </div>
    </div>

    <script src="./JS/主页.js"></script>
    <script src="./libs/chart.js"></script>
    <script>
        // 确保Chart.js加载完成后再初始化图表
        window.addEventListener('load', function() {
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载成功');
            } else {
                console.log('Chart.js本地文件加载成功');
                initAllCharts();
            }
        });
        
        function initAllCharts() {
            // 重新初始化所有图表
            if (typeof renderWorkHourChart === 'function') {
                renderWorkHourChart();
            }
            if (typeof renderStatBonusStackedBarChart === 'function') {
                renderStatBonusStackedBarChart();
            }
        }
    </script>
</body>
</html>
