<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价值工时测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>价值工时功能测试</h1>
    
    <div class="test-section">
        <h2>测试结果</h2>
        <div id="test-results"></div>
    </div>

    <script>
        function runTests() {
            const results = [];
            
            // 测试1: 检查菜单项是否存在
            try {
                const iframe = document.createElement('iframe');
                iframe.src = '价值工时.html';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                
                iframe.onload = function() {
                    const doc = iframe.contentDocument;
                    
                    // 检查菜单项
                    const menuItem = doc.querySelector('[data-page="my-value-hours"]');
                    if (menuItem) {
                        results.push('<div class="success">✓ 菜单项"我的价值工时"存在</div>');
                    } else {
                        results.push('<div class="error">✗ 菜单项"我的价值工时"不存在</div>');
                    }
                    
                    // 检查页面内容
                    const page = doc.getElementById('my-value-hours');
                    if (page) {
                        results.push('<div class="success">✓ 页面内容存在</div>');
                    } else {
                        results.push('<div class="error">✗ 页面内容不存在</div>');
                    }
                    
                    // 检查表格
                    const table = doc.getElementById('valueHoursTable');
                    if (table) {
                        results.push('<div class="success">✓ 价值工时表格存在</div>');
                    } else {
                        results.push('<div class="error">✗ 价值工时表格不存在</div>');
                    }
                    
                    // 检查新增按钮
                    const addBtn = doc.getElementById('addValueHourBtn');
                    if (addBtn) {
                        results.push('<div class="success">✓ 新增按钮存在</div>');
                    } else {
                        results.push('<div class="error">✗ 新增按钮不存在</div>');
                    }
                    
                    // 检查模态框
                    const modal = doc.getElementById('valueHourModal');
                    if (modal) {
                        results.push('<div class="success">✓ 模态框存在</div>');
                    } else {
                        results.push('<div class="error">✗ 模态框不存在</div>');
                    }
                    
                    document.getElementById('test-results').innerHTML = results.join('');
                    document.body.removeChild(iframe);
                };
            } catch (error) {
                results.push('<div class="error">✗ 测试过程中出现错误: ' + error.message + '</div>');
                document.getElementById('test-results').innerHTML = results.join('');
            }
        }
        
        // 运行测试
        runTests();
    </script>
</body>
</html>
