# 我的价值工时功能说明

## 功能概述
在奖金管理模块中新增了"我的价值工时"页面，用于管理个人的价值工时记录。

## 功能特性

### 1. 页面导航
- 在左侧菜单的"奖金管理"部分添加了"我的价值工时"菜单项
- 点击菜单项可切换到价值工时管理页面

### 2. 数据表格
表格包含以下字段：
- **任务编号**: 任务的唯一标识
- **任务描述**: 任务的详细描述
- **任务金额**: 任务的总金额
- **计划工时**: 预计完成任务所需的工时
- **申报工时**: 实际申报的工时
- **单价价值工时**: 单位价值工时
- **确认价值工时**: 最终确认的价值工时
- **任务状态**: 未封闭/已封闭
- **交通费**: 任务相关的交通费用
- **住宿费**: 任务相关的住宿费用
- **其他费用**: 其他相关费用
- **工效比**: 自动计算的工作效率比例
- **补贴系数**: 补贴系数
- **合同属性**: 合同相关属性
- **负责人**: 任务负责人
- **备注**: 备注信息
- **操作**: 编辑和删除按钮

### 3. 操作功能

#### 新增任务
- 点击"新增任务"按钮打开新增模态框
- 填写所有必要信息
- 工效比会根据计划工时和申报工时自动计算
- 点击"保存"按钮保存记录

#### 编辑任务
- 点击表格行中的"编辑"按钮
- 在模态框中修改任务信息
- 保存修改后的信息

#### 删除任务
- 点击表格行中的"删除"按钮
- 确认删除操作
- 从列表中移除该记录

### 4. 自动计算功能
- **工效比计算**: 工效比 = (计划工时 / 申报工时) × 100%
- 当修改计划工时或申报工时时，工效比会自动更新

## 技术实现

### 前端结构
- **HTML**: 页面结构和模态框
- **CSS**: 使用现有的样式系统，保持界面一致性
- **JavaScript**: 数据管理和交互逻辑

### 数据存储
- 当前使用前端数组存储数据（演示用）
- 可以轻松扩展为后端API集成

### 样式一致性
- 使用与其他页面相同的表格样式
- 操作按钮样式与系统其他部分保持一致
- 模态框设计遵循现有的设计规范

## 使用说明

1. **访问页面**: 在左侧菜单中点击"奖金管理" → "我的价值工时"
2. **查看数据**: 页面会显示当前所有的价值工时记录
3. **新增记录**: 点击"新增任务"按钮，填写表单并保存
4. **编辑记录**: 点击任意记录的"编辑"按钮进行修改
5. **删除记录**: 点击任意记录的"删除"按钮进行删除

## 注意事项

- 任务编号和任务描述为必填项
- 工效比会根据计划工时和申报工时自动计算
- 删除操作需要确认，删除后无法恢复
- 所有数据修改会立即生效

## 扩展建议

1. **数据持久化**: 集成后端API进行数据存储
2. **数据验证**: 添加更严格的表单验证
3. **导出功能**: 支持数据导出为Excel等格式
4. **筛选功能**: 添加按状态、时间等条件筛选
5. **统计功能**: 添加工时统计和分析图表
