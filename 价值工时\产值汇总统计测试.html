<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产值汇总统计功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .test-item { margin: 10px 0; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>产值汇总统计功能测试</h1>
    
    <div class="test-section">
        <h2>功能检查清单</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>数据验证</h2>
        <div id="data-validation"></div>
    </div>

    <script>
        function runTests() {
            const results = [];
            const dataResults = [];
            
            try {
                const iframe = document.createElement('iframe');
                iframe.src = '价值工时.html';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                
                iframe.onload = function() {
                    const doc = iframe.contentDocument;
                    const win = iframe.contentWindow;
                    
                    // 检查季度产值表格
                    const quarterlyTable = doc.getElementById('quarterlyOutputTable');
                    if (quarterlyTable) {
                        results.push('<div class="success">✓ 季度产值表格存在</div>');
                    } else {
                        results.push('<div class="error">✗ 季度产值表格不存在</div>');
                    }
                    
                    // 检查搜索筛选区域
                    const searchFilters = doc.querySelector('.search-filters');
                    if (searchFilters) {
                        results.push('<div class="success">✓ 搜索筛选区域存在</div>');
                    } else {
                        results.push('<div class="error">✗ 搜索筛选区域不存在</div>');
                    }
                    
                    // 检查年度筛选下拉框
                    const filterYear = doc.getElementById('filterYear');
                    if (filterYear) {
                        results.push('<div class="success">✓ 年度筛选下拉框存在</div>');
                        
                        // 检查选项
                        const options = filterYear.querySelectorAll('option');
                        if (options.length >= 3) {
                            results.push('<div class="success">✓ 年度选项充足（' + options.length + '个）</div>');
                        } else {
                            results.push('<div class="error">✗ 年度选项不足</div>');
                        }
                    } else {
                        results.push('<div class="error">✗ 年度筛选下拉框不存在</div>');
                    }
                    
                    // 检查新增按钮
                    const addBtn = doc.getElementById('addQuarterlyOutputBtn');
                    if (addBtn) {
                        results.push('<div class="success">✓ 新增季度产值按钮存在</div>');
                    } else {
                        results.push('<div class="error">✗ 新增季度产值按钮不存在</div>');
                    }
                    
                    // 检查搜索按钮
                    const searchBtn = doc.getElementById('searchBtn');
                    if (searchBtn) {
                        results.push('<div class="success">✓ 搜索按钮存在</div>');
                    } else {
                        results.push('<div class="error">✗ 搜索按钮不存在</div>');
                    }
                    
                    // 检查模态框
                    const modal = doc.getElementById('quarterlyOutputModal');
                    if (modal) {
                        results.push('<div class="success">✓ 季度产值模态框存在</div>');
                    } else {
                        results.push('<div class="error">✗ 季度产值模态框不存在</div>');
                    }
                    
                    // 检查筛选字段
                    const filterFields = [
                        'filterOrderId', 'filterTaskId', 'filterResponsible', 
                        'filterOrderType', 'filterTaskStatus', 'filterInstitution',
                        'filterDepartment', 'filterGroup'
                    ];
                    
                    let foundFields = 0;
                    filterFields.forEach(fieldId => {
                        if (doc.getElementById(fieldId)) {
                            foundFields++;
                        }
                    });
                    
                    if (foundFields === filterFields.length) {
                        results.push('<div class="success">✓ 所有筛选字段存在（' + foundFields + '个）</div>');
                    } else {
                        results.push('<div class="error">✗ 筛选字段不完整（' + foundFields + '/' + filterFields.length + '）</div>');
                    }
                    
                    // 数据验证
                    if (win.quarterlyOutputData) {
                        dataResults.push('<div class="success">✓ 季度产值数据存在</div>');
                        
                        const years = Object.keys(win.quarterlyOutputData);
                        dataResults.push('<div class="info">📊 包含年度: ' + years.join(', ') + '</div>');
                        
                        years.forEach(year => {
                            const yearData = win.quarterlyOutputData[year];
                            dataResults.push('<div class="info">📈 ' + year + '年数据: ' + yearData.length + '条记录</div>');
                        });
                    } else {
                        dataResults.push('<div class="error">✗ 季度产值数据不存在</div>');
                    }
                    
                    // 检查函数是否存在
                    const functions = [
                        'renderQuarterlyOutputTable',
                        'openQuarterlyOutputModal',
                        'closeQuarterlyOutputModal',
                        'filterByYear',
                        'initQuarterlyOutput'
                    ];
                    
                    let foundFunctions = 0;
                    functions.forEach(funcName => {
                        if (typeof win[funcName] === 'function') {
                            foundFunctions++;
                        }
                    });
                    
                    if (foundFunctions === functions.length) {
                        results.push('<div class="success">✓ 所有必要函数存在（' + foundFunctions + '个）</div>');
                    } else {
                        results.push('<div class="error">✗ 函数不完整（' + foundFunctions + '/' + functions.length + '）</div>');
                    }
                    
                    document.getElementById('test-results').innerHTML = results.join('');
                    document.getElementById('data-validation').innerHTML = dataResults.join('');
                    document.body.removeChild(iframe);
                };
            } catch (error) {
                results.push('<div class="error">✗ 测试过程中出现错误: ' + error.message + '</div>');
                document.getElementById('test-results').innerHTML = results.join('');
            }
        }
        
        // 运行测试
        runTests();
    </script>
</body>
</html>
